defmodule MisReports.Repo.Migrations.CreateWkltaskAttachments do
  use Ecto.Migration

  def change do
    create table(:wkltask_attachments) do
      add :attachment_id, :integer
      add :userid, :string
      add :reference, :string
      add :file_name, :string
      add :attachment_location, :string
      add :created_date, :date
      add :restrict_to, :integer
      add :file_comment, :string

      timestamps()
    end
  end
end
