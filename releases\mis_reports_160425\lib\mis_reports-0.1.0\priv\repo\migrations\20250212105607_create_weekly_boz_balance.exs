defmodule MisReports.Repo.Migrations.CreateWeeklyBozBalance do
  use Ecto.Migration

  def change do
    create table(:weekly_boz_balance) do
      add :current_account_boz, :decimal
      add :omo_term_deposits, :decimal
      add :omo_repo_placements, :decimal
      add :col_interbank_loans_made, :decimal
      add :col_interbank_loans_received, :decimal
      add :kwacha_statutory_reserve, :decimal
      add :fcy_statutory_reserve, :decimal
      add :new_kwacha_loans_agriculture, :decimal
      add :new_fcy_loans_agriculture, :decimal
      add :report_dt, :date
      add :status, :string
      add :maker_id, references(:tbl_users, on_delete: :nothing)
      add :checker_id, references(:tbl_users, on_delete: :nothing)


      timestamps()
    end
  end
end
