defmodule MisReports.Repo.Migrations.CreateModules do
  use Ecto.Migration

  def change do
    create table(:modules) do
      add :module_id, :integer
      add :name, :string
      add :code, :string
      add :description, :text
      add :status, :string
      add :created_by, :integer
      add :created_date, :date
      add :modified_by, :integer
      add :modified_date, :date

      timestamps()
    end
  end
end
