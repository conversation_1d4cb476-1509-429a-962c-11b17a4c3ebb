defmodule MisReports.Repo.Migrations.CreateWkltaskDetailsHistory do
  use Ecto.Migration

  def change do
    create table(:wkltask_details_history) do
      add :detail_id_history, :integer
      add :detail_id, :integer
      add :header_id, :integer
      add :step_id, :integer
      add :action_id, :integer
      add :user_id, :string
      add :functional_office_id, :integer
      add :start_date, :date
      add :end_date, :date
      add :invisible, :boolean, default: false, null: false
      add :comments, :text
      add :upload, :text
      add :privilege_id, :integer
      add :previous_user_id, :string
      add :previous_user_name, :string

      timestamps()
    end
  end
end
