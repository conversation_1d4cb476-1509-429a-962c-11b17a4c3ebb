defmodule MisReportsWeb.WeeklyLive.Index do
  use MisReportsWeb, :live_view
  use MisReportsWeb, :custom_component
  on_mount MisReportsWeb.UserLiveAuth
  import MisReportsWeb.Components.Shared.WeeklyNavigationMenu
  require Logger
  alias MisReportsWeb.LiveHelpers
  alias MisReportsWeb.PrudentialController
  alias MisReports.Workers.Weekly.CLA.Sh27
  alias MisReportsWeb.Components.Shared.WeeklyNavigationMenu
  # Add this line
  alias MisReports.Utilities.WeeklyFileExport
  # Add this line if not already present
  alias MisReports.Repo
  alias MisReportsWeb.UserController

  defp normalize_step_name(step_name) when is_binary(step_name) do
    step_name
    |> String.trim()
    |> String.downcase()
    |> String.replace(~r/\s+/, "_")
    |> String.replace(~r/[^a-z0-9_]/, "")
  end

  defp normalize_step_name(_), do: ""

  @review_steps [
    "weekly_return_review_stage",
    "weekly_return_report_approval_level_1",
    "weekly_return_report_approval_by_cfo",
    "weekly_return_report_approval_by_ce"
  ]

  # Update the view_configs to use the working modules
  @view_configs %{
    "2000" => %{
      "core_liquid_assets" => [
        "schedule_27",
        "schedule_27A1",
        "schedule_27A2",
        "schedule_27A3",
        "schedule_27A4",
        "schedule_27A5",
        "schedule_27B1",
        "schedule_27B2"
      ]
    },
    "3000" => %{
      "forex_risk" => [
        "schedule_21A",
        "schedule_21B"
      ]
    }
  }

  defp assign_defaults(socket, session) do
    socket
    |> assign(:current_user, MisReports.Accounts.get_user!(session["current_user"]))
    |> assign(:session_timeout_at, session["session_timeout_at"])
    |> assign(:session_timeout?, false)
    |> assign(:settings, MisReports.Utilities.get_comapany_settings_params())
    |> assign(:export_filename, nil)
    |> assign(:export_fx_filename, nil)
    |> assign(:export_cla_filename, nil)
  end

  # Check for existing completed exports and set the export_filename
  defp check_for_completed_exports(%{assigns: %{reference: reference}} = socket) when not is_nil(reference) do
    # Get all completed exports for this reference
    completed_exports = MisReports.Utilities.get_all_completed_weekly_exports_by_reference(reference)

    # Initialize socket with default export filenames
    socket = socket
      |> assign(:export_filename, nil)
      |> assign(:export_fx_filename, nil)
      |> assign(:export_cla_filename, nil)

    # Process each completed export based on its export_type
    Enum.reduce(completed_exports, socket, fn export, acc_socket ->
      case export.export_type do
        "FOREX_RISK" ->
          assign(acc_socket, :export_fx_filename, export.filename)

        "CORE_LIQUID_ASSETS" ->
          assign(acc_socket, :export_cla_filename, export.filename)

        _ ->
          # For backward compatibility, set the generic export_filename
          assign(acc_socket, :export_filename, export.filename)
      end
    end)
  end

  defp check_for_completed_exports(socket), do: socket

  defp check_view_only(params) do
    cond do
      # Check if view parameter is explicitly set to "true"
      params["view"] == "true" ->
        true

      # Default case - not view only
      true ->
        false
    end
  end

  @impl true
  def mount(params, session, socket) do
    if connected?(socket) && params["reference"] do
      # Subscribe to both export completion and status change events
      Phoenix.PubSub.subscribe(MisReports.PubSub, "weekly_export:#{params["reference"]}")
      # Subscribe to the export data channel to receive cell values
      Phoenix.PubSub.subscribe(MisReports.PubSub, "weekly_export_data:#{params["reference"]}")
      Logger.info("Subscribed to weekly_export:#{params["reference"]} and weekly_export_data:#{params["reference"]} channels")
    end

    # Initialize export_filename to nil and cell_values to an empty list
    socket = assign(socket, :export_filename, nil)
    socket = assign(socket, :cell_values, [])
    socket = assign(socket, :show_cell_values, false)

    # Check if required parameters are present
    if is_nil(params["process_id"]) || is_nil(params["step_id"]) || is_nil(params["reference"]) do
      # Redirect to a default view with minimal functionality
      socket =
        socket
        |> assign(:step_id, nil)
        |> assign(:reference, nil)
        |> assign(:process_id, "2000") # Default to Core Liquid Assets
        |> assign(:view_only, true)
        |> assign_defaults(session)
        |> assign(:report_type, "")
        |> assign(:current_view, "core_liquid_assets")
        |> assign(:active_tab, "core_liquid_assets")
        |> assign(:loader, false)
        |> assign(:save_btn, false)
        |> assign(:confirm_btn, false)
        |> assign(:accounts, "")
        |> assign(:download_btn, false)
        |> assign(:show_modal, false)
        |> assign(:is_fullscreen, false)
        |> assign(:entries, [])
        |> assign(:filter_params, %{"date" => Date.utc_today() |> Date.to_string()})
        |> assign(:data, [])
        |> assign(:header, gen_header(Date.utc_today() |> Date.to_string()))
        |> assign(:show_helper, true)
        |> assign(:comments, [])
        |> assign(:helper_clicked, false)
        |> assign(:review_steps, @review_steps)
        |> assign(:comment, nil)
        |> assign(:pending_comments, %{})
        |> put_flash(:info, "Please select a report to view")

      {:ok, socket}
    else
      view_only = check_view_only(params)
      process_id = params["process_id"]

      # Determine initial view and schedule based on process
      {initial_view, initial_schedule} =
        case process_id do
          "2000" -> {"core_liquid_assets", "schedule_27"}
          "3000" -> {"forex_risk", "schedule_21A"}
          # default
          _ -> {"core_liquid_assets", "schedule_27"}
        end

      socket =
        socket
        |> assign(:step_id, params["step_id"])
        |> assign(:reference, params["reference"])
        |> assign(:process_id, process_id)
        |> assign(:view_only, view_only)
        |> assign_defaults(session)

      # Continue with the original mount function
      # Get step name early
      step_name =
        case params["step_id"] do
          nil -> nil
          step_id -> get_step_name(step_id) |> normalize_step_name()
        end

      # Check if it's a review step
      is_review = step_name in @review_steps

      assigns = [
        report_type: if(is_review, do: initial_schedule, else: ""),
        current_view: initial_view,
        active_tab: initial_view,
        loader: is_review,
        save_btn: true,
        confirm_btn: false,
        accounts: "",
        download_btn: false,
        show_modal: false,
        is_fullscreen: false,
        entries: [],
        filter_params: %{"date" => Date.utc_today() |> Date.to_string()},
        data: [],
        header: gen_header(Date.utc_today() |> Date.to_string()),
        show_helper: true,
        comments: [],
        helper_clicked: false,
        review_steps: @review_steps,
        # Add comment-related assigns
        comment: nil,
        pending_comments: %{},
        # Add modal-related assigns
        show_comment_modal: false,
        current_schedule: nil,
        current_schedule_name: nil,
        modal_comment: ""
      ]

      socket =
        socket
        |> assign(assigns)
        |> restore_comment_log()
        |> maybe_load_comments()
        |> maybe_load_saved_period()
        |> check_for_completed_exports()

      # Update the review step data loading in mount function
      if is_review do
        case MisReports.Workflow.get_report_period(params["reference"]) do
          nil ->
            socket =
              socket
              |> put_flash(:error, "Report period not found")
              |> assign(loader: false)

            {:ok, socket}

          period ->
            # Get the correct initial schedule based on process_id
            {view, schedule} =
              case process_id do
                "3000" -> {"forex_risk", "schedule_21A"}
                "2000" -> {"core_liquid_assets", "schedule_27"}
                _ -> {"core_liquid_assets", "schedule_27"}
              end

            # Start async task to load data with correct schedule
            Task.async(fn ->
              result = handle_schedule_generation(schedule, Date.to_string(period.start_date))
              send(self(), {:load_schedule, result, %{"date" => period.start_date}, schedule})
            end)

            {:ok,
             socket
             |> assign(:loader, true)
             |> assign(:filter_params, %{
               "date" => period.start_date,
               "start_date" => period.start_date
             })}
        end
      else
        {:ok, socket}
      end
    end


  end

  defp maybe_load_comments(%{assigns: %{step_id: nil}} = socket), do: socket

  defp maybe_load_comments(%{assigns: %{step_id: step_id, reference: reference}} = socket) do
    if normalize_step_name(get_step_name(step_id)) in @review_steps do
      case MisReports.Workflow.get_task_comments(reference) do
        nil -> socket
        comments -> assign(socket, :comments, comments)
      end
    else
      socket
    end
  end

  defp maybe_load_saved_period(%{assigns: %{reference: reference}} = socket)
       when not is_nil(reference) do
    case MisReports.Workflow.get_report_period(reference) do
      nil ->
        socket

      saved_report ->
        socket
        |> assign(:filter_params, %{"date" => saved_report.start_date})
        |> assign(:saved_report, saved_report)
    end
  end

  defp maybe_load_saved_period(socket), do: socket

  defp get_step_name(nil), do: ""

  defp get_step_name(step_id) do
    MisReports.Workflow.get_wklstep_rule!(step_id).step_name
  end

  defp get_view_only_status(session) do
    case session["step_id"] do
      nil ->
        false

      step_id ->
        step_name =
          get_step_name(step_id)
          |> normalize_step_name()

        step_name in @review_steps and step_name != "weekly_report_generation"
    end
  end

  @impl true
  def handle_params(params, _url, socket) do
    # Set 15-second timeout for helper message
    Process.send_after(self(), :hide_helper, 15000)

    menu_opts =
      LiveHelpers.menu_opts(__MODULE__, socket.assigns.live_action, [
        :edit,
        :audit_log
      ])

    {:noreply,
     socket
     |> assign(menu_opts: menu_opts)
     |> apply_action(socket.assigns.live_action, params)}
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(page: %{prev: "Prudential", current: "Reports"})
  end

  @impl true
  def handle_event("view_page", %{"value" => schedule_type}, socket) do
    data = socket.assigns.entries
    saved_report = nil
    adjustments = nil
    filter_params = socket.assigns.filter_params
    header = gen_header(filter_params["date"])
    new_weekly_report?(saved_report, schedule_type, data, filter_params, adjustments)

    assigns = [
      report_type: schedule_type,
      loader: true,
      filter_params: filter_params,
      data: data,
      header: header
    ]

    {:noreply, assign(socket, assigns)}
  end

  # Update the switch_view handler
  @impl true
  def handle_event("switch_view", %{"view" => view}, socket) do
    process_id = socket.assigns.process_id
    schedules = get_in(@view_configs, [process_id, view])

    case schedules do
      nil ->
        {:noreply,
         socket
         |> put_flash(:error, "Invalid view configuration")
         |> assign(:current_view, view)
         |> assign(:active_tab, view)
         |> assign(:report_type, "")}

      [first_schedule | _] ->
        # Use existing report generation logic
        date = ensure_date_string(socket.assigns.filter_params["date"])

        Task.async(fn ->
          result = handle_schedule_generation(first_schedule, date)
          send(self(), {:load_schedule, result, socket.assigns.filter_params, first_schedule})
        end)

        {:noreply,
         socket
         |> assign(:current_view, view)
         |> assign(:active_tab, view)
         |> assign(:report_type, first_schedule)
         |> assign(:loader, true)}
    end
  end

  defp ensure_date_string(date) when is_binary(date), do: date
  defp ensure_date_string(%Date{} = date), do: Date.to_string(date)
  defp ensure_date_string(_), do: Date.utc_today() |> Date.to_string()

  @impl true
  def handle_event("filter-report", %{"report" => params}, socket) do
    date = params["date"]
    step_name = normalize_step_name(get_step_name(socket.assigns.step_id))
    process_id = socket.assigns.process_id
    current_view = socket.assigns.current_view

    cond do
      # Handle Review Steps
      step_name in @review_steps ->
        case MisReports.Workflow.get_report_period(socket.assigns.reference) do
          nil ->
            {:noreply,
             socket
             |> put_flash(:error, "Report period not found")
             |> assign(loader: false)}

          period ->
            date = Date.to_string(period.start_date)
            # Get first schedule for the current view
            first_schedule = get_in(@view_configs, [process_id, current_view]) |> List.first()

            socket = assign(socket, loader: true)

            # Start async task using handle_schedule_generation
            Task.async(fn ->
              result = handle_schedule_generation(first_schedule, date)
              send(self(), {:load_schedule, result, %{"date" => date}, first_schedule})
            end)

            {:noreply,
             socket
             |> assign(:current_view, current_view)
             |> assign(:active_tab, current_view)
             |> assign(:report_type, first_schedule)}
        end

      # Handle Initial Submission
      step_name == "weekly_report_generation" ->
        socket = handle_period_check(socket, params)
        # Get first schedule for the current view
        first_schedule = get_in(@view_configs, [process_id, current_view]) |> List.first()

        socket = assign(socket, :loader, true)

        # Start async task using handle_schedule_generation
        Task.async(fn ->
          result = handle_schedule_generation(first_schedule, date)
          send(self(), {:load_schedule, result, %{"date" => date}, first_schedule})
        end)

        {:noreply,
         socket
         |> assign(:current_view, current_view)
         |> assign(:active_tab, current_view)
         |> assign(:report_type, first_schedule)}

      # Default case
      true ->
        Logger.debug("No matching step found")
        {:noreply, socket}
    end
  end

  @impl true
  def handle_info({:load_schedule, schedule_type}, socket) do
    filter_params = socket.assigns.filter_params
    process_id = socket.assigns.process_id

    result =
      case {schedule_type, process_id} do
        # Core Liquid Assets (2000)
        {"schedule_27", "2000"} ->
          MisReports.Workers.ReportGens.Weekly.CoreLiquidAssets.Sh27.generate_display(
            filter_params["date"]
          )

        {"schedule_27A1", "2000"} ->
          MisReports.Workers.ReportGens.Weekly.CoreLiquidAssets.Sh27a1.generate_display(
            filter_params["date"]
          )

        {"schedule_27A2", "2000"} ->
          MisReports.Workers.ReportGens.Weekly.CoreLiquidAssets.Sh27a2.generate_display(
            filter_params["date"]
          )

        {"schedule_27A3", "2000"} ->
          MisReports.Workers.ReportGens.Weekly.CoreLiquidAssets.Sh27a3.generate_display(
            filter_params["date"]
          )

        {"schedule_27A4", "2000"} ->
          MisReports.Workers.ReportGens.Weekly.CoreLiquidAssets.Sh27a4.generate_display(
            filter_params["date"]
          )

        {"schedule_27A5", "2000"} ->
          MisReports.Workers.ReportGens.Weekly.CoreLiquidAssets.Sh27a5.generate_display(
            filter_params["date"]
          )

        {"schedule_27B1", "2000"} ->
          MisReports.Workers.ReportGens.Weekly.CoreLiquidAssets.Sh27b1.generate_display(
            filter_params["date"]
          )

        {"schedule_27B2", "2000"} ->
          MisReports.Workers.ReportGens.Weekly.CoreLiquidAssets.Sh27b2.generate_display(
            filter_params["date"]
          )

        # Forex Risk (3000)
        {"schedule_21A", "3000"} ->
          MisReports.Workers.ReportGens.Weekly.CoreLiquidAssets.Sh21a.generate_display(
            filter_params["date"]
          )

        {"schedule_21B", "3000"} ->
          data =
            MisReports.Workers.ReportGens.Weekly.CoreLiquidAssets.Sh21b.generate_display(
              filter_params["date"]
            )

          data[:data]

        _ ->
          []
      end

    {:noreply,
     socket
     |> assign(:data, result)
     |> assign(:loader, false)}
  end

  @impl true
  def handle_info(:hide_helper, socket) do
    {:noreply, assign(socket, :show_helper, false)}
  end

  def clean_data(entries) do
    Enum.map(entries, fn e ->
      case String.length(e.sap_gl_acc_no) > 6 do
        true -> Map.put(e, :sap_gl_acc_no, String.slice(e.sap_gl_acc_no, 4..-1))
        false -> e
      end
    end)
  end

  def page_name(:index), do: "Income Statement"

  @impl true
  def handle_info({_ref, {:load_schedule, result, filter_params, schedule_type}}, socket) do
    assigns = [
      report_type: schedule_type,
      loader: false,
      filter_params: filter_params,
      data: result,
      download_btn: false
    ]

    {:noreply, assign(socket, assigns)}
  end

  @impl true
  def handle_info({:DOWN, _ref, :process, _pid, _reason}, socket) do
    IO.inspect("Task has completed", label: "Task Monitoring")
    {:noreply, socket}
  end

  defp new_weekly_report?(_saved_report, schedule_type, data, filter_params, _adjustments) do
    filter_params =
      Map.update!(filter_params, "date", fn date ->
        cond do
          # Handle empty date
          date == "" ->
            Date.utc_today() |> Date.to_string()

          # Handle existing Date struct
          is_struct(date, Date) ->
            Date.to_string(date)

          # Handle string date
          is_binary(date) ->
            case Date.from_iso8601(date) do
              {:ok, parsed_date} -> Date.to_string(parsed_date)
              _ -> Date.utc_today() |> Date.to_string()
            end

          # Handle any other case
          true ->
            Date.utc_today() |> Date.to_string()
        end
      end)

    Task.async(fn ->
      result =
        case schedule_type do
          "schedule_27" ->
            MisReports.Workers.ReportGens.Weekly.CoreLiquidAssets.Sh27.generate_display(
              filter_params["date"]
            )

          "schedule_27A1" ->
            MisReports.Workers.ReportGens.Weekly.CoreLiquidAssets.Sh27a1.generate_display(
              filter_params["date"]
            )

          "schedule_27A2" ->
            MisReports.Workers.ReportGens.Weekly.CoreLiquidAssets.Sh27a2.generate_display(
              filter_params["date"]
            )

          "schedule_27A3" ->
            MisReports.Workers.ReportGens.Weekly.CoreLiquidAssets.Sh27a3.generate_display(
              filter_params["date"]
            )

          "schedule_27A4" ->
            MisReports.Workers.ReportGens.Weekly.CoreLiquidAssets.Sh27a4.generate_display(
              filter_params["date"]
            )

          "schedule_27A5" ->
            MisReports.Workers.ReportGens.Weekly.CoreLiquidAssets.Sh27a5.generate_display(
              filter_params["date"]
            )

          "schedule_27B1" ->
            MisReports.Workers.ReportGens.Weekly.CoreLiquidAssets.Sh27b1.generate_display(
              filter_params["date"]
            )

          "schedule_27B2" ->
            MisReports.Workers.ReportGens.Weekly.CoreLiquidAssets.Sh27b2.generate_display(
              filter_params["date"]
            )

          "schedule_21A" ->
            MisReports.Workers.ReportGens.Weekly.CoreLiquidAssets.Sh21a.generate_display(
              filter_params["date"]
            )

          "schedule_21B" ->
            data =
              MisReports.Workers.ReportGens.Weekly.CoreLiquidAssets.Sh21b.generate_display(
                filter_params["date"]
              )

            data[:data]

          _ ->
            []
        end

      send(self(), {:load_schedule, result, filter_params, schedule_type})
    end)
  end

  def gen_header(date) do
    %{
      inst_code: "0031000",
      inst_name: "Stanbic Bank",
      date: format_date(date)
    }
  end

  def format_date(%Date{} = date) do
    # Handle Date struct
    month_name = Timex.month_name(date.month)
    days_in_month = Date.days_in_month(date)
    "#{date.day} #{month_name} #{date.year} (Normal days: #{days_in_month})"
  end

  def format_date(date) when is_binary(date) do
    # Handle string date
    [y, m, d] = String.split(date, "-")

    month_name =
      case m do
        "01" -> "January"
        "02" -> "February"
        "03" -> "March"
        "04" -> "April"
        "05" -> "May"
        "06" -> "June"
        "07" -> "July"
        "08" -> "August"
        "09" -> "September"
        "10" -> "October"
        "11" -> "November"
        "12" -> "December"
        _ -> "Invalid Month"
      end

    days_in_month =
      case {m, String.to_integer(y)} do
        {"02", year} when rem(year, 4) == 0 and (rem(year, 100) != 0 or rem(year, 400) == 0) ->
          "29"

        {"02", _} ->
          "28"

        {"04", _} ->
          "30"

        {"06", _} ->
          "30"

        {"09", _} ->
          "30"

        {"11", _} ->
          "30"

        {_, _} ->
          "31"
      end

    "#{String.to_integer(d)} #{month_name} #{y} (Normal days: #{days_in_month})"
  end

  def get_schedule_name(schedule) do
    case schedule do
      "schedule_27" -> "Schedule 27"
      "schedule_27A1" -> "Schedule 27A1"
      "schedule_27A2" -> "Schedule 27A2"
      "schedule_27A3" -> "Schedule 27A3"
      "schedule_27A4" -> "Schedule 27A4"
      "schedule_27A5" -> "Schedule 27A5"
      "schedule_27B1" -> "Schedule 27B1"
      "schedule_27B2" -> "Schedule 27B2"
      "schedule_21A" -> "Schedule 21A"
      "schedule_21B" -> "Schedule 21B"
      _ -> schedule
    end
  end

  @impl true
  def handle_event("toggle_fullscreen", _params, socket) do
    {:noreply, assign(socket, is_fullscreen: !socket.assigns.is_fullscreen)}
  end

  defp handle_final_approval(socket, comment) do
    current_user_id = to_string(socket.assigns.current_user.id)

    # Clear the comment log since workflow is completing
    socket = clear_comment_log(socket)

    # First handle the save_schedule
    send(self(), {:save_schedule})

    # Update report period status
    case MisReports.Workflow.update_report_period_status(socket.assigns.reference, "approved") do
      {:ok, _period} ->
        # Then call workflow
        case MisReports.Workflow.call_workflow(
               socket.assigns.reference,
               socket.assigns.process_id,
               current_user_id,
               97,
               "",
               "",
               comment
             ) do
          {:ok, _reference_number} ->
            {:noreply,
             socket
             |> clear_pending_comments()
             |> put_flash(:info, "Weekly Report Process completed successfully")
             |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}

          {:error, reason} ->
            {:noreply,
             socket
             |> put_flash(:error, "Final approval failed: #{reason}")
             |> assign(:loader, false)}
        end

      {:error, reason} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to update report status: #{reason}")
         |> assign(:loader, false)}
    end
  end

  # Update the save handler to use handle_final_approval with automatic comment aggregation
  @impl true
  def handle_event("save", %{"action" => action, "comment" => comment} = _params, socket) do
    # **AUTOMATIC COMMENT AGGREGATION FOR WORKFLOW SUBMISSION**
    # Ensure all schedule-specific comments are included in the final comment
    final_comment = if String.trim(comment || "") == "" do
      # If no additional comment provided, use only aggregated schedule comments
      socket.assigns.comment || ""
    else
      # The comment field already contains aggregated schedule comments
      # The user may have added additional review comments at the top
      comment
    end

    if String.trim(final_comment) == "" do
      {:noreply, socket |> put_flash(:error, "Please add a comment or provide schedule-specific feedback before proceeding")}
    else
      case action do
        "97" ->
          if normalize_step_name(get_step_name(socket.assigns.step_id)) == "weekly_return_report_approval_by_ce" do
            handle_final_approval(socket, final_comment)
          else
            handle_approval(socket, final_comment)
          end

        "96" ->
          handle_rejection(socket, final_comment)

        _ ->
          {:noreply, socket |> put_flash(:error, "Invalid action")}
      end
    end
  end

  # Handle opening the comment modal using LiveView assigns
  @impl true
  def handle_event("open_comment_modal", %{"schedule" => schedule}, socket) do
    schedule_name = format_schedule_name(schedule)

    {:noreply,
     socket
     |> assign(:show_comment_modal, true)
     |> assign(:current_schedule, schedule)
     |> assign(:current_schedule_name, schedule_name)
     |> assign(:modal_comment, "")}
  end

  # Handle closing the comment modal
  @impl true
  def handle_event("close_comment_modal", _params, socket) do
    {:noreply,
     socket
     |> assign(:show_comment_modal, false)
     |> assign(:current_schedule, nil)
     |> assign(:current_schedule_name, nil)
     |> assign(:modal_comment, "")}
  end

  # Handle saving a comment for a specific schedule - aggregated comments only (no database storage)
  @impl true
  def handle_event("save_comment", %{"comment" => comment}, socket) do
    # Get schedule from socket assigns
    schedule = socket.assigns.current_schedule

    if is_nil(schedule) or String.trim(comment) == "" do
      {:noreply, socket |> put_flash(:error, "Please enter a comment")}
    else
      # Format the schedule name for display
      schedule_name = format_schedule_name(schedule)

      # Format the comment to include the schedule name (no database storage)
      formatted_comment = "[#{schedule_name}] - #{comment}"

      # **ROBUST DUPLICATE PREVENTION - REBUILD COMMENT TEXT FROM SCRATCH**
      # 1. Get current state
      pending_comments = socket.assigns.pending_comments || %{}
      reference_number = socket.assigns.reference

      # 2. Update the individual comment for this schedule (this is the source of truth)
      updated_pending_comments = Map.put(pending_comments, schedule, comment)

      # 3. Rebuild the entire comment text from the updated pending_comments map
      # This ensures no duplicates and maintains consistency
      rebuilt_comment = rebuild_comment_text_from_pending(updated_pending_comments, reference_number)

      # 4. Determine action message
      existing_comment = Map.get(pending_comments, schedule)
      action_message = if existing_comment do
        "Comment updated for #{schedule_name}"
      else
        "Comment saved for #{schedule_name}"
      end

      # 5. Update comment log for persistence
      comment_log_key = "#{reference_number}_comment_log"
      final_pending_comments = Map.put(updated_pending_comments, comment_log_key, rebuilt_comment)

      {:noreply,
       socket
       |> assign(:comment, rebuilt_comment)
       |> assign(:pending_comments, final_pending_comments)
       |> assign(:show_comment_modal, false)
       |> assign(:current_schedule, nil)
       |> assign(:current_schedule_name, nil)
       |> assign(:modal_comment, "")
       |> put_flash(:info, "#{action_message}. Total comments: #{count_comments(rebuilt_comment)}")
       |> push_event("hide-comment-modal", %{})
       |> push_event("update-main-comment", %{comment: rebuilt_comment})}
    end
  end

  # Handle canceling a comment
  @impl true
  def handle_event("cancel_comment", _params, socket) do
    {:noreply, socket}
  end

  # Handle receiving pending comments from client - ENHANCED WITH SYNC FIX
  @impl true
  def handle_event("pending_comments_data", %{"comments" => comments_json}, socket) do
    # Parse the JSON string of comments
    case Jason.decode(comments_json) do
      {:ok, comments_map} ->
        # Combine all comments into one string for the main comment field
        combined_comments = combine_pending_comments(comments_map, socket.assigns.reference)

        # Update both the display comment and the persistent storage
        reference_number = socket.assigns.reference
        comment_log_key = "#{reference_number}_comment_log"

        # Update pending_comments with the synchronized data
        updated_pending_comments = socket.assigns.pending_comments
        |> Map.merge(comments_map)  # Merge individual schedule comments
        |> Map.put(comment_log_key, combined_comments)  # Update comment log

        {:noreply,
         socket
         |> assign(:comment, combined_comments)
         |> assign(:pending_comments, updated_pending_comments)}

      {:error, _} ->
        # If there's an error parsing the JSON, just continue without changing comments
        {:noreply, socket}
    end
  end

  @impl true
  def handle_event("submit_report", _params, socket) do
    current_user_id = to_string(socket.assigns.current_user.id)

    case MisReports.Workflow.call_workflow(
           socket.assigns.reference,
           socket.assigns.process_id,
           current_user_id,
           # Submit action code
           80,
           "",
           "",
           "Weekly Return Report Submission"
         ) do
      {:ok, reference_number} ->
        {:noreply,
         socket
         |> put_flash(
           :info,
           "Weekly return submitted successfully. Reference: #{reference_number}"
         )
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}

      {:error, reason} ->
        {:noreply, socket |> put_flash(:error, "Failed to submit report: #{reason}")}
    end
  end

  defp handle_approval(socket, comment) do
    current_user_id = to_string(socket.assigns.current_user.id)

    case MisReports.Workflow.call_workflow(
           socket.assigns.reference,
           socket.assigns.process_id,
           current_user_id,
           # Approval action code
           97,
           "",
           "",
           comment
         ) do
      {:ok, _} ->
        {:noreply,
         socket
         |> clear_pending_comments()
         |> put_flash(:info, "Weekly return approved successfully")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}

      {:error, reason} ->
        {:noreply, socket |> put_flash(:error, "Failed to approve report: #{reason}")}
    end
  end

  # Helper function to clear pending comments
  defp clear_pending_comments(socket) do
    push_event(socket, "clear-pending-comments", %{})
  end

  # Helper function to format schedule ID into a readable name
  defp format_schedule_name(schedule) do
    case schedule do
      "schedule_" <> number -> "Schedule #{String.upcase(number)}"
      _ -> schedule
    end
  end

  # Helper function to count comments in the comment string
  defp count_comments(comment_string) when is_binary(comment_string) do
    comment_string
    |> String.split("\n")
    |> Enum.filter(fn line -> String.trim(line) != "" end)
    |> length()
  end
  defp count_comments(_), do: 0

  # Helper function to restore comment log from pending comments
  defp restore_comment_log(socket) do
    reference_number = socket.assigns.reference
    pending_comments = socket.assigns.pending_comments
    comment_log_key = "#{reference_number}_comment_log"

    case Map.get(pending_comments, comment_log_key) do
      nil -> socket
      "" -> socket
      comment_log -> assign(socket, :comment, comment_log)
    end
  end

  # Helper function to clear comment log (only called on final approval/rejection)
  defp clear_comment_log(socket) do
    reference_number = socket.assigns.reference
    pending_comments = socket.assigns.pending_comments
    comment_log_key = "#{reference_number}_comment_log"

    updated_pending_comments = Map.delete(pending_comments, comment_log_key)

    socket
    |> assign(:comment, "")
    |> assign(:pending_comments, updated_pending_comments)
  end

  # Helper function to combine all pending comments into one string
  defp combine_pending_comments(comments_map, reference) do
    # Filter comments that belong to the current reference
    reference_prefix = "#{reference}_"

    comments_map
    |> Enum.filter(fn {key, _} -> String.starts_with?(key, reference_prefix) end)
    |> Enum.map(fn {key, comment} ->
      # Extract schedule ID from the key
      schedule = String.replace_prefix(key, reference_prefix, "")
      # Format the schedule name
      schedule_name = format_schedule_name(schedule)
      # Format the comment
      "[#{schedule_name}] - #{comment}"
    end)
    |> Enum.join("\n")
  end

  # **ROBUST DUPLICATE PREVENTION HELPER**
  # Rebuild comment text from pending_comments map to ensure no duplicates
  defp rebuild_comment_text_from_pending(pending_comments, reference) do
    # Filter out only the individual schedule comments (not log entries)
    schedule_comments = pending_comments
    |> Enum.filter(fn {key, _value} ->
      # Include only schedule keys, exclude log keys and other metadata
      String.match?(key, ~r/^schedule_/) and not String.contains?(key, "_comment_log")
    end)
    |> Enum.map(fn {schedule_id, comment_text} ->
      # Format each comment consistently
      schedule_name = format_schedule_name(schedule_id)
      "[#{schedule_name}] - #{comment_text}"
    end)
    |> Enum.sort()  # Sort for consistent ordering
    |> Enum.join("\n")

    schedule_comments
  end

  defp handle_rejection(socket, comment) do
    current_user_id = to_string(socket.assigns.current_user.id)

    # Clear the comment log since workflow is completing
    socket = clear_comment_log(socket)

    case MisReports.Workflow.call_workflow(
           socket.assigns.reference,
           socket.assigns.process_id,
           current_user_id,
           # Rejection action code
           96,
           "",
           "",
           comment
         ) do
      {:ok, _} ->
        {:noreply,
         socket
         |> clear_pending_comments()
         |> put_flash(:info, "Weekly return rejected")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}

      {:error, reason} ->
        {:noreply, socket |> put_flash(:error, "Failed to reject report: #{reason}")}
    end
  end

  defp handle_period_check(socket, params) do
    IO.inspect(params)

    IO.inspect(
      normalize_step_name(get_step_name(socket.assigns.step_id)) == "weekly_report_generation",
      label: "Step Check"
    )

    if normalize_step_name(get_step_name(socket.assigns.step_id)) == "weekly_report_generation" do
      IO.inspect(MisReports.Workflow.get_report_period(socket.assigns.reference),
        label: "Report Period Check"
      )

      case MisReports.Workflow.get_report_period(socket.assigns.reference) do
        nil -> create_new_period(socket, params)
        existing -> update_existing_period(socket, existing, params)
      end
    else
      socket
    end
  end

  defp create_new_period(socket, params) do
    IO.inspect(params, label: "Create New Period Params")

    period_attrs = %{
      reference: socket.assigns.reference,
      start_date: Date.from_iso8601!(params["date"]),
      end_date: Timex.shift(Date.from_iso8601!(params["date"]), weeks: -1),
      report_type: "Weekly_Return",
      status: "pending"
    }

    case MisReports.Workflow.create_report_period(period_attrs) do
      {:ok, _period} ->
        IO.inspect(params, label: "Create New Period Params")
        socket

      {:error, _} ->
        socket
        |> put_flash(:error, "Failed to save report period")
        |> assign(loader: false)
    end
  end

  defp update_existing_period(socket, existing, params) do
    update_attrs = %{
      start_date: Date.from_iso8601!(params["date"]),
      end_date: Timex.shift(Date.from_iso8601!(params["date"]), weeks: -1)
    }

    case MisReports.Workflow.update_report_period(existing, update_attrs) do
      {:ok, _updated_period} ->
        socket

      {:error, _} ->
        socket
        |> put_flash(:error, "Failed to update report period date")
        |> assign(loader: false)
    end
  end

  @impl true
  def handle_event("helper_clicked", _params, socket) do
    {:noreply, assign(socket, :helper_clicked, true)}
  end

  # Helper function to format comment text with bold schedule titles
  def format_comment_with_bold_schedules(comment) do
    # Split the comment by newlines to handle existing line breaks
    lines = String.split(comment, "\n")

    # Process each line
    formatted_lines =
      Enum.map(lines, fn line ->
        # Check if this line contains a schedule reference
        if Regex.match?(~r/\[(.*?)\]/, line) do
          # Format the schedule reference with bold styling
          Regex.replace(~r/\[(.*?)\] - (.*)/, line, fn _, schedule, text ->
            "<div class=\"schedule-comment\"><strong class=\"schedule-tag\">#{schedule}</strong> #{text}</div>"
          end)
        else
          # Regular line without schedule reference
          "<div>#{line}</div>"
        end
      end)

    # Join the formatted lines and return as raw HTML
    Phoenix.HTML.raw(Enum.join(formatted_lines, ""))
  end

  # Helper function to get valid views for a process
  def get_valid_views(process_id) do
    @view_configs
    |> Map.get(process_id, %{})
    |> Map.keys()
  end

  # Helper function to get default view for a process
  def get_default_view(process_id) do
    @view_configs
    |> get_in([process_id])
    |> case do
      nil -> nil
      views -> views |> Map.keys() |> List.first()
    end
  end

  # Add helper function for schedule generation
  defp handle_schedule_generation(schedule_type, date) do
    case schedule_type do
      "schedule_27" ->
        MisReports.Workers.ReportGens.Weekly.CoreLiquidAssets.Sh27.generate_display(date)

      "schedule_27A1" ->
        MisReports.Workers.ReportGens.Weekly.CoreLiquidAssets.Sh27a1.generate_display(date)

      "schedule_27A2" ->
        MisReports.Workers.ReportGens.Weekly.CoreLiquidAssets.Sh27a2.generate_display(date)

      "schedule_27A3" ->
        MisReports.Workers.ReportGens.Weekly.CoreLiquidAssets.Sh27a3.generate_display(date)

      "schedule_27A4" ->
        MisReports.Workers.ReportGens.Weekly.CoreLiquidAssets.Sh27a4.generate_display(date)

      "schedule_27A5" ->
        MisReports.Workers.ReportGens.Weekly.CoreLiquidAssets.Sh27a5.generate_display(date)

      "schedule_27B1" ->
        MisReports.Workers.ReportGens.Weekly.CoreLiquidAssets.Sh27b1.generate_display(date)

      "schedule_27B2" ->
        MisReports.Workers.ReportGens.Weekly.CoreLiquidAssets.Sh27b2.generate_display(date)

      "schedule_21A" ->
        MisReports.Workers.ReportGens.Weekly.CoreLiquidAssets.Sh21a.generate_display(date)

      "schedule_21B" ->
        data = MisReports.Workers.ReportGens.Weekly.CoreLiquidAssets.Sh21b.generate_display(date)
        data[:data]

      _ ->
        []
    end
  end

  @impl true
  def handle_info({:save_schedule}, socket) do
    IO.inspect("====================== SAVING DATA =============================")
    report_date = to_string(socket.assigns.filter_params["date"])
    month = String.replace(report_date, "-", "") |> String.slice(4..5)
    year = String.replace(report_date, "-", "") |> String.slice(0..3)
    process_id = socket.assigns.process_id
    reference = socket.assigns.reference
    audit_msg = "Created New Weekly report for Month: #{month} and Year: #{year}"
    user = socket.assigns.current_user
    socket = assign(socket, loader: false)

    # Parse the report_date string to a Date struct
    {:ok, date} = Date.from_iso8601(report_date)

    Ecto.Multi.new()
    |> Ecto.Multi.insert(
      :report,
      WeeklyFileExport.changeset(
        %WeeklyFileExport{maker_id: user.id},
        %{
          report_date: report_date,
          process_id: process_id,
          reference: reference,
          week: "#{elem(Timex.iso_week(date), 1)}",
          year: year,
          status: "PENDING"  # Explicitly set status to PENDING
        }
      )
    )
    |> UserController.audit_log(user.id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, %{report: report, audit_log: _user_log}} ->
        # Broadcast the status update to the LiveView
        Phoenix.PubSub.broadcast(
          MisReports.PubSub,
          "weekly_export:#{report.reference}",
          {:weekly_export_status_changed, report}
        )

        message = %{message: %{info: "Weekly report confirmation successful. Report is now in PENDING status."}}

        {:noreply, push_event(socket, "notification", message)}

      {:error, _failed_operation, failed_value, _changes_so_far} ->
        IO.inspect(failed_value, label: "Failed Value")
        error_msg = UserController.traverse_errors(failed_value.errors) |> Enum.join("\r\n")
        message = %{message: %{error: "Weekly report Failed to load"}}

        {:noreply, push_event(socket, "notification", message)}
        # error_msg = UserController.traverse_errors(failed_value.errors) |> Enum.join("\r\n")
        # {:noreply, push_event(socket, "notification", %{message: %{error: error_msg}})}
    end
  end

  def handle_event("save-report", _params, socket) do
    send(self(), {:save_schedule})
    assigns = [loader: true]
    {:noreply, assign(socket, assigns)}
  end

  @impl true
  def handle_event("toggle_cell_values", _params, socket) do
    # Toggle the show_cell_values flag
    current_state = socket.assigns.show_cell_values
    {:noreply, assign(socket, :show_cell_values, !current_state)}
  end

  @impl true
  def handle_event("export_excel", _params, socket) do
    require Logger
    # Show the loading indicator briefly
    socket = assign(socket, loader: true, loader_message: "Initiating export...")

    # Reset the cell values list
    socket = assign(socket, cell_values: [])

    report_date = to_string(socket.assigns.filter_params["date"])
    process_id = socket.assigns.process_id
    reference = socket.assigns.reference
    user = socket.assigns.current_user

    Logger.info("Export Excel initiated for reference: #{reference}, process_id: #{process_id}, date: #{report_date}")

    # Parse the report_date string to a Date struct
    {:ok, date} = Date.from_iso8601(report_date)

    # Determine report type based on process_id
    report_type = case process_id do
      "2000" -> "CORE_LIQUID_ASSETS"
      "3000" -> "FOREX_RISK"
      _ -> "UNKNOWN"
    end

    Logger.info("Export type determined: #{report_type}")

    # Generate a default filename with the correct format based on export type
    report_type_abbr = case report_type do
      "CORE_LIQUID_ASSETS" -> "CLA"
      "FOREX_RISK" -> "FX"
      _ -> "UNKNOWN"
    end
    default_filename = "Weekly_#{report_type_abbr}_Report_#{report_date}.xlsx"
    Logger.info("Generated default filename: #{default_filename}")

    # Turn off loader immediately before database operation
    socket = assign(socket, :loader, false)

    # Get existing export record with the same export type if it exists
    existing_export = MisReports.Utilities.get_weekly_exports_by_reference_and_type(reference, report_type)
    |> Enum.at(0)

    # Always create a new record with status PENDING
    MisReports.Repo.insert(%MisReports.Utilities.WeeklyFileExport{
      report_date: Date.from_iso8601!(report_date),
      process_id: process_id,
      reference: reference,
      maker_id: user.id,
      status: "PENDING",
      export_type: report_type,
      week: "#{elem(Timex.iso_week(date), 1)}",
      year: "#{date.year}",
      filename: default_filename
    })
    |> case do
      {:ok, new_export} ->
        Logger.info("Created new weekly export record with filename: #{new_export.filename} and reference: #{reference}")

        # Broadcast the status update to the LiveView
        Phoenix.PubSub.broadcast(
          MisReports.PubSub,
          "weekly_export:#{new_export.reference}",
          {:weekly_export_status_changed, new_export}
        )

        {:noreply,
          socket
          |> assign(:export_filename, new_export.filename)
          |> put_flash(:info, "Export initiated. The system is processing your request in the background.")}

      {:error, changeset} ->
        error_msg = traverse_errors(changeset.errors) |> Enum.join("\r\n")
        Logger.error("Failed to create weekly export record: #{error_msg}")
        {:noreply,
          socket
          |> put_flash(:error, "Failed to initiate export: #{error_msg}")}
    end
  end

  @impl true
  def handle_info({:redirect_to_download, url}, socket) do
    {:noreply,
      socket
      |> assign(:loader, false)
      |> put_flash(:info, "Excel export initiated. The file will be available for download shortly.")
      |> redirect(external: url)}
  end

  @impl true
  def handle_info({:weekly_export_complete, updated_export}, socket) do
    # Update the appropriate filename in the socket assigns based on export_type
    socket = case updated_export.export_type do
      "FOREX_RISK" ->
        socket
        |> assign(:export_fx_filename, updated_export.filename)

      "CORE_LIQUID_ASSETS" ->
        socket
        |> assign(:export_cla_filename, updated_export.filename)

      _ ->
        socket
        |> assign(:export_filename, updated_export.filename)
    end

    # Return with updated socket and flash message - no need to set loader to false as it should already be off
    {:noreply,
      socket
      |> put_flash(:info, "Excel export completed successfully. You can now click the Download button to download the file.")}
  end

  @impl true
  def handle_info({:weekly_export_data_warning, %{sheet: sheet, message: message}}, socket) do
    # Handle the warning message by showing a flash message to the user
    Logger.warning("Export warning for #{sheet}: #{message}")

    # Create a more user-friendly message
    user_message = case sheet do
      "Schedule 21B" ->
        "The #{sheet} sheet contains only zero values. This is normal if there are no foreign currency placements for the selected period. The report has been generated successfully with a Totals row showing zero values."
      "Schedule 21A" ->
        "The #{sheet} sheet may contain limited data. This is normal if there are limited foreign exchange transactions for the selected period. The report has been generated with the available data."
      _ ->
        "#{message} (#{sheet})"
    end

    {:noreply,
      socket
      |> assign(:loader, false)
      |> put_flash(:info, user_message)}
  end

  @impl true
  def handle_info({:weekly_export_status_changed, updated_export}, socket) do
    # Update the UI when the status changes
    message = case updated_export.status do
      "PENDING" -> "Export request received. Processing will begin shortly."
      "PENDING_EXPORT" -> "Report data is being prepared. Excel generation will begin shortly."
      "EXPORT_COMPLETE" -> "Excel export completed successfully. You can now click the Download button to download the file."
      "EXPORT_ERROR" -> "Error occurred during export. Please try again."
      _ -> "Export status updated to: #{updated_export.status}"
    end

    # Update the appropriate filename in the socket assigns if status is EXPORT_COMPLETE
    socket = if updated_export.status == "EXPORT_COMPLETE" do
      case updated_export.export_type do
        "FOREX_RISK" ->
          socket
          |> assign(:export_fx_filename, updated_export.filename)

        "CORE_LIQUID_ASSETS" ->
          socket
          |> assign(:export_cla_filename, updated_export.filename)

        _ ->
          socket
          |> assign(:export_filename, updated_export.filename)
      end
    else
      socket
    end

    # Never turn on the loader for status updates - we want to keep it off
    # to avoid blocking the UI during background processing

    {:noreply,
      socket
      |> put_flash(:info, message)}
  end

  @impl true
  def handle_info({:weekly_export_data, %{sheet: sheet, cells: cells}}, socket) do
    # Add the new cells to the existing cell_values list
    current_cells = socket.assigns.cell_values
    updated_cells = current_cells ++ cells

    # Show the cell values panel
    {:noreply,
      socket
      |> assign(:cell_values, updated_cells)
      |> assign(:show_cell_values, true)}
  end

  @impl true
  def handle_event("export_fx", _params, socket) do
    require Logger
    # Show the loading indicator briefly
    socket = assign(socket, loader: true, loader_message: "Initiating export...")

    # Reset the cell values list
    socket = assign(socket, cell_values: [])

    report_date = to_string(socket.assigns.filter_params["date"])
    process_id = socket.assigns.process_id
    reference = socket.assigns.reference
    user = socket.assigns.current_user

    Logger.info("Export FX Report initiated for reference: #{reference}, process_id: #{process_id}, date: #{report_date}")

    # Parse the report_date string to a Date struct
    {:ok, date} = Date.from_iso8601(report_date)

    # Generate a filename for the FX report with the correct format
    fx_filename = "Weekly_FX_Report_#{report_date}.xlsx"

    # Turn off loader immediately before database operation
    socket = assign(socket, :loader, false)

    # Get the existing export record if it exists
    existing_export = MisReports.Utilities.get_weekly_export_by_reference(reference)

    # Always create a new record with status PENDING
    MisReports.Repo.insert(%MisReports.Utilities.WeeklyFileExport{
      report_date: Date.from_iso8601!(report_date),
      process_id: process_id,
      reference: reference,
      maker_id: user.id,
      status: "PENDING",
      export_type: "FOREX_RISK",
      week: "#{elem(Timex.iso_week(date), 1)}",
      year: "#{date.year}",
      filename: fx_filename
    })
    |> case do
      {:ok, new_export} ->
        Logger.info("Created new weekly export record for FX report with reference: #{reference}")

        # Broadcast the status update to the LiveView
        Phoenix.PubSub.broadcast(
          MisReports.PubSub,
          "weekly_export:#{new_export.reference}",
          {:weekly_export_status_changed, new_export}
        )

        {:noreply,
          socket
          |> assign(:export_fx_filename, fx_filename)
          |> put_flash(:info, "FX Export initiated. The system is processing your request in the background.")}

      {:error, changeset} ->
        error_msg = traverse_errors(changeset.errors) |> Enum.join("\r\n")
        Logger.error("Failed to create weekly export record for FX report: #{error_msg}")

        {:noreply,
          socket
          |> put_flash(:error, "Failed to initiate FX export: #{error_msg}")}
    end
  end

  @impl true
  def handle_event("export_cla", _params, socket) do
    require Logger
    # Show the loading indicator briefly
    socket = assign(socket, loader: true, loader_message: "Initiating export...")

    # Reset the cell values list
    socket = assign(socket, cell_values: [])

    report_date = to_string(socket.assigns.filter_params["date"])
    process_id = socket.assigns.process_id
    reference = socket.assigns.reference
    user = socket.assigns.current_user

    Logger.info("Export CLA Report initiated for reference: #{reference}, process_id: #{process_id}, date: #{report_date}")

    # Parse the report_date string to a Date struct
    {:ok, date} = Date.from_iso8601(report_date)

    # Generate a filename for the CLA report with the correct format
    cla_filename = "Weekly_CLA_Report_#{report_date}.xlsx"

    # Turn off loader immediately before database operation
    socket = assign(socket, :loader, false)

    # Get existing export record with the same export type if it exists
    existing_export = MisReports.Utilities.get_weekly_exports_by_reference_and_type(reference, "CORE_LIQUID_ASSETS")
    |> Enum.at(0)

    # Always create a new record with status PENDING
    MisReports.Repo.insert(%MisReports.Utilities.WeeklyFileExport{
      report_date: Date.from_iso8601!(report_date),
      process_id: process_id,
      reference: reference,
      maker_id: user.id,
      status: "PENDING",
      export_type: "CORE_LIQUID_ASSETS",
      week: "#{elem(Timex.iso_week(date), 1)}",
      year: "#{date.year}",
      filename: cla_filename
    })
    |> case do
      {:ok, new_export} ->
        Logger.info("Created new weekly export record for CLA report with reference: #{reference}")

        # Broadcast the status update to the LiveView
        Phoenix.PubSub.broadcast(
          MisReports.PubSub,
          "weekly_export:#{new_export.reference}",
          {:weekly_export_status_changed, new_export}
        )

        {:noreply,
          socket
          |> assign(:export_cla_filename, cla_filename)
          |> put_flash(:info, "CLA Export initiated. The system is processing your request in the background.")}

      {:error, changeset} ->
        error_msg = traverse_errors(changeset.errors) |> Enum.join("\r\n")
        Logger.error("Failed to create weekly export record for CLA report: #{error_msg}")

        {:noreply,
          socket
          |> put_flash(:error, "Failed to initiate CLA export: #{error_msg}")}
    end
  end



  # Helper function to handle period check
  defp handle_period_check(socket, params) do
    # This function checks if a report period exists for the given date
    # and creates one if it doesn't exist
    date = params["date"]

    # Return the socket with updated filter params
    assign(socket, :filter_params, %{"date" => date})
  end

  # Helper function to handle schedule generation
  defp handle_schedule_generation(schedule_type, date) do
    case schedule_type do
      "schedule_27" ->
        MisReports.Workers.ReportGens.Weekly.CoreLiquidAssets.Sh27.generate_display(date)

      "schedule_27A1" ->
        MisReports.Workers.ReportGens.Weekly.CoreLiquidAssets.Sh27a1.generate_display(date)

      "schedule_27A2" ->
        MisReports.Workers.ReportGens.Weekly.CoreLiquidAssets.Sh27a2.generate_display(date)

      "schedule_27A3" ->
        MisReports.Workers.ReportGens.Weekly.CoreLiquidAssets.Sh27a3.generate_display(date)

      "schedule_27A4" ->
        MisReports.Workers.ReportGens.Weekly.CoreLiquidAssets.Sh27a4.generate_display(date)

      "schedule_27A5" ->
        MisReports.Workers.ReportGens.Weekly.CoreLiquidAssets.Sh27a5.generate_display(date)

      "schedule_27B1" ->
        MisReports.Workers.ReportGens.Weekly.CoreLiquidAssets.Sh27b1.generate_display(date)

      "schedule_27B2" ->
        MisReports.Workers.ReportGens.Weekly.CoreLiquidAssets.Sh27b2.generate_display(date)

      "schedule_21A" ->
        MisReports.Workers.ReportGens.Weekly.CoreLiquidAssets.Sh21a.generate_display(date)

      "schedule_21B" ->
        data = MisReports.Workers.ReportGens.Weekly.CoreLiquidAssets.Sh21b.generate_display(date)
        data[:data]

      _ ->
        []
    end
  end

  # Helper function to traverse changeset errors
  defp traverse_errors(errors) do
    for {key, {msg, _opts}} <- errors do
      "#{key} #{msg}"
    end
  end
end
