defmodule MisReports.Repo.Migrations.CreateWklrequests do
  use Ecto.Migration

  def change do
    create table(:wklrequests) do
      add :request_id, :integer
      add :reference_number, :string
      add :process_id, :integer
      add :user_id, :string
      add :action_id, :integer
      add :previous_reference_number, :string
      add :upload, :text
      add :comment, :text
      add :status, :string
      add :created_by, :string
      add :created_date, :date
      add :modified_by, :string
      add :modified_date, :date

      timestamps()
    end
  end
end
