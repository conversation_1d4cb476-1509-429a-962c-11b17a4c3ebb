defmodule MisReports.Repo.Migrations.AddIndicesToWeeklyReportEntriesAndGmoTpins do
  use Ecto.Migration

  def change do
    # Add an index to speed up joins between WeeklyReportEntriesCustContribution and gmo_tpins
    create index("weekly_report_entries_cust_contribution", [:customer_number_global_cif])
    create index("gmo_tpins", [:customer_number])

    # Add a composite index on the date and currency_code columns for filtering
    create index("weekly_report_entries_cust_contribution", [:date, :currency_code])

    # Add an index to support filtering on actual_credit_balance
    create index("weekly_report_entries_cust_contribution", [:actual_credit_balance])

    # Add an index on tax_identification_number for grouping
    create index("gmo_tpins", [:tax_identification_number])

    # If you need to optimize retrieval of account_number, balance_sap_ledger_no, and account_name, consider additional indices
    create index("weekly_report_entries_cust_contribution", [:account_number])
    create index("weekly_report_entries_cust_contribution", [:balance_sap_ledger_no])
    create index("weekly_report_entries_cust_contribution", [:account_name])
  end
end
