{application,gen_smtp,
             [{description,"A generic Erlang SMTP server/client framework"},
              {vsn,"0.15.0"},
              {modules,[binstr,gen_smtp_application,gen_smtp_client,
                        gen_smtp_server,gen_smtp_server_session,mimemail,
                        smtp_rfc822_parse,smtp_server_example,smtp_socket,
                        smtp_util]},
              {applications,[kernel,stdlib,crypto,asn1,public_key,ssl]},
              {registered,[]},
              {licenses,["BSD 2-clause"]},
              {links,[{"GitHub","https://github.com/Vagabond/gen_smtp"}]},
              {exclude_files,["src/smtp_rfc822_parse.erl"]}]}.
