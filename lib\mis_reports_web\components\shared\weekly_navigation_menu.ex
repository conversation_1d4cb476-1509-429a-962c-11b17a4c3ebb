defmodule MisReportsWeb.Components.Shared.WeeklyNavigationMenu do
  use Phoenix.Component

  # Add attribute validation
  attr :current_view, :string, required: true
  attr :process_id, :string, required: true
  attr :report_type, :string, required: true

  def render_navigation_menu(assigns) do
    ~H"""
    <div class="overflow">
      <%= for schedule <- get_schedules(@current_view, @process_id) do %>
        <div class="mt-1 divide-y divide-gray-200 border-b border-t border-gray-200">
          <div class="flex flex-col py-3 hover:bg-gray-50 text-sm font-medium card transition ease-in-out delay-150 hover:-translate-y-1 hover:scale-110 duration-300 px-2 rounded">
            <div phx-click="view_page" phx-disable-with="Loading..." phx-value-value={schedule} class="mb-1">
              <div value={schedule} class={"text-gray-500 #{if @report_type == schedule, do: "linkselector", else: nil}"}>
                <%= get_schedule_title(schedule) %>
              </div>
            </div>
            <%= if not assigns[:view_only] do %>
            <div class="mt-2">
              <%
                # Check if this schedule has an existing comment
                has_comment = assigns[:pending_comments] && Map.has_key?(assigns[:pending_comments], schedule)
                button_class = if has_comment do
                  "w-full flex justify-center items-center px-2 py-1 text-xs font-medium rounded-md text-orange-700 bg-orange-50 hover:bg-orange-100 focus:outline-none focus:ring-2 focus:ring-offset-0 focus:ring-orange-500"
                else
                  "w-full flex justify-center items-center px-2 py-1 text-xs font-medium rounded-md text-blue-700 bg-blue-50 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-0 focus:ring-blue-500"
                end
                button_text = if has_comment, do: "Edit Comment", else: "Comment"
              %>
              <button
                 class={button_class}
                 id={"comment-link-#{schedule}"}
                 phx-click="open_comment_modal"
                 phx-value-schedule={schedule}
                 title={if has_comment, do: "Edit existing comment for this schedule", else: "Add comment for this schedule"}>
                <svg class="mr-1 h-3 w-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
                </svg>
                <%= button_text %>
              </button>
            </div>
            <% end %>
          </div>
        </div>
      <% end %>
    </div>
    """
  end

  @view_configs %{
    "2000" => %{
      "core_liquid_assets" => [
        "schedule_27",
        "schedule_27A1",
        "schedule_27A2",
        "schedule_27A3",
        "schedule_27A4",
        "schedule_27A5",
        "schedule_27B1",
        "schedule_27B2"
      ]
    },
    "3000" => %{
      "forex_risk" => [
        "schedule_21A",
        "schedule_21B"
      ]
    }
  }

  # Default schedules as fallback
  @default_schedules %{
    "core_liquid_assets" => [
      "schedule_27",
      "schedule_27A1",
      "schedule_27A2",
      "schedule_27A3",
      "schedule_27A4",
      "schedule_27A5",
      "schedule_27B1",
      "schedule_27B2"
    ],
    "forex_risk" => [
      "schedule_21A",
      "schedule_21B"
    ]
  }

  def get_schedules(view, process_id) when is_binary(process_id) do
    # Try to get process-specific schedules first
    case get_in(@view_configs, [process_id, view]) do
      nil -> Map.get(@default_schedules, view, @default_schedules["core_liquid_assets"])
      schedules -> schedules
    end
  end

  def get_schedules(view, _) do
    # Fallback to default schedules if process_id is nil or invalid
    Map.get(@default_schedules, view, @default_schedules["core_liquid_assets"])
  end

  def get_schedule_title(schedule), do: MisReportsWeb.WeeklyLive.Index.get_schedule_name(schedule)
end
