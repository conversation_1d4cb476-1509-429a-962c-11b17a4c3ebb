%%
%% %CopyrightBegin%
%% 
%% Copyright Ericsson AB 1996-2023. All Rights Reserved.
%% 
%% Licensed under the Apache License, Version 2.0 (the "License");
%% you may not use this file except in compliance with the License.
%% You may obtain a copy of the License at
%%
%%     http://www.apache.org/licenses/LICENSE-2.0
%%
%% Unless required by applicable law or agreed to in writing, software
%% distributed under the License is distributed on an "AS IS" BASIS,
%% WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
%% See the License for the specific language governing permissions and
%% limitations under the License.
%% 
%% %CopyrightEnd%
%%
%% This is an -*- erlang -*- file.
%%
{application, kernel,
 [
  {description, "ERTS  CXC 138 10"},
  {vsn, "9.1"},
  {modules, [application,
	     application_controller,
	     application_master,
	     application_starter,
	     auth,
	     code,
	     code_server,
	     dist_util,
	     erl_boot_server,
	     erl_compile_server,
	     erl_distribution,
             erl_erts_errors,
	     erl_reply,
             erl_kernel_errors,
             erl_signal_handler,
	     erpc,
	     error_handler,
	     error_logger,
	     file,
             file_server,
             file_io_server,
	     global,
	     global_group,
	     global_search,
	     group,
	     group_history,
	     heart,
	     inet6_tcp,
	     inet6_tcp_dist,
	     inet6_udp,
	     inet6_sctp,
	     inet_config,
             inet_epmd_dist,
             inet_epmd_socket,
	     inet_hosts,
	     inet_gethost_native,
	     inet_tcp_dist,
	     kernel,
	     kernel_config,
	     kernel_refc,
	     local_tcp,
	     local_udp,
             logger,
             logger_backend,
             logger_config,
             logger_disk_log_h,
             logger_filters,
             logger_formatter,
             logger_h_common,
             logger_handler_watcher,
             logger_olp,
             logger_proxy,
             logger_server,
             logger_simple_h,
             logger_std_h,
             logger_sup,
	     net,
	     net_adm,
	     net_kernel,
	     os,
	     ram_file,
	     rpc,
	     user_drv,
	     user_sup,
             prim_tty,
             disk_log,
             disk_log_1,
             disk_log_server,
             disk_log_sup,
             dist_ac,
             erl_ddll,
             erl_epmd,
	     erts_debug,
             gen_tcp,
	     gen_tcp_socket,
             gen_udp,
	     gen_udp_socket,
	     gen_sctp,
             inet,
             inet_db,
             inet_dns,
             inet_parse,
             inet_res,
             inet_tcp,
             inet_udp,
	     inet_sctp,
             pg,
             pg2,
             raw_file_io,
             raw_file_io_compressed,
             raw_file_io_deflate,
             raw_file_io_delayed,
             raw_file_io_inflate,
             raw_file_io_list,
	     seq_trace,
             socket,
	     standard_error,
	     wrap_log_reader]},
  {registered, [application_controller,
		erl_reply,
		auth,
		boot_server,
		code_server,
		disk_log_server,
		disk_log_sup,
		erl_prim_loader,
		error_logger,
		file_server_2,
		fixtable_server,
		global_group,
		global_name_server,
		heart,
		init,
		kernel_config,
		kernel_refc,
		kernel_sup,
                logger,
                logger_handler_watcher,
                logger_sup,
		net_kernel,
		net_sup,
		rex,
		user,
	        os_server,
                ddll_server,
                erl_epmd,
                inet_db,
                pg]},
  {applications, []},
  {env, [{logger_level, notice},
         {logger_sasl_compatible, false},
         {net_tickintensity, 4},
         {net_ticktime, 60},
         {prevent_overlapping_partitions, true},
         {shell_docs_ansi,auto}
        ]},
  {mod, {kernel, []}},
  {runtime_dependencies, ["erts-14.0", "stdlib-5.0",
                          "sasl-3.0", "crypto-5.0"]}
  ]
}.
