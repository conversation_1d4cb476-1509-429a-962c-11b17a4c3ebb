defmodule MisReports.Repo.Migrations.CreateWklactionRules do
  use Ecto.Migration

  def change do
    create table(:wklaction_rules) do
      add :action_rule_id, :integer
      add :process_id, :integer
      add :action_id, :integer
      add :step_id, :integer
      add :next_process_id, :integer
      add :next_step_id, :integer
      add :rvt_user_step, :integer
      add :created_by, :string
      add :created_date, :date
      add :modified_by, :string
      add :modified_date, :date
      add :status, :string

      timestamps()
    end
  end
end
