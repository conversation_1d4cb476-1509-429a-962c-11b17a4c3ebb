defmodule MisReports.Repo.Migrations.CreateNostroAccounts do
  use Ecto.Migration

  def change do
    create table(:nostro_accounts) do
      add :report_date, :date
      add :account_number, :string
      add :account_name, :string
      add :branch, :string
      add :ccy, :string
      add :scheme_descrpt, :string
      add :code, :string
      add :subhead_descrpt, :string
      add :source, :string
      add :book_bal1, :decimal, precision: 18, scale: 2
      add :cleared_bal1, :decimal, precision: 18, scale: 2
      add :lcy_book_bal1, :decimal, precision: 18, scale: 2
      add :lcy_cleared_bal1, :decimal, precision: 18, scale: 2
      add :book_bal2, :decimal, precision: 18, scale: 2
      add :cleared_bal2, :decimal, precision: 18, scale: 2
      add :lcy_book_bal2, :decimal, precision: 18, scale: 2
      add :lcy_cleared_bal2, :decimal, precision: 18, scale: 2
      add :book_bal3, :decimal, precision: 18, scale: 2
      add :cleared_bal3, :decimal, precision: 18, scale: 2
      add :lcy_book_bal3, :decimal, precision: 18, scale: 2
      add :lcy_cleared_bal3, :decimal, precision: 18, scale: 2
      add :book_bal4, :decimal, precision: 18, scale: 2
      add :cleared_bal4, :decimal, precision: 18, scale: 2
      add :lcy_book_bal4, :decimal, precision: 18, scale: 2
      add :lcy_cleared_bal4, :decimal, precision: 18, scale: 2
      add :book_bal5, :decimal, precision: 18, scale: 2
      add :cleared_bal5, :decimal, precision: 18, scale: 2
      add :lcy_book_bal5, :decimal, precision: 18, scale: 2
      add :lcy_cleared_bal5, :decimal, precision: 18, scale: 2
      add :book_bal6, :decimal, precision: 18, scale: 2
      add :cleared_bal6, :decimal, precision: 18, scale: 2
      add :lcy_book_bal6, :decimal, precision: 18, scale: 2
      add :lcy_cleared_bal6, :decimal, precision: 18, scale: 2
      add :book_bal7, :decimal, precision: 18, scale: 2
      add :cleared_bal7, :decimal, precision: 18, scale: 2
      add :lcy_book_bal7, :decimal, precision: 18, scale: 2
      add :lcy_cleared_bal7, :decimal, precision: 18, scale: 2
      add :src_file_id, :bigint

      timestamps()
    end
  end
end
