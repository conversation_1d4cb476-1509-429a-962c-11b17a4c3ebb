# DateTimeParser Playground

## Installation

Install DateTimeParser in your project

```elixir
Mix.install([:date_time_parser])
```

## Usage

Use DateTimeParser to parse strings into DateTime, NaiveDateTime, Date, or Time
structs. For example:

```elixir
[
  DateTimeParser.parse_datetime("2021-11-09T10:30:00Z"),
  DateTimeParser.parse_datetime("2021-11-09T10:30:00"),
  DateTimeParser.parse_date("2021-11-09T10:30:00Z"),
  DateTimeParser.parse_time("2021-11-09T10:30:00Z"),
  DateTimeParser.parse("2021-32-32T10:30:00Z"),
  DateTimeParser.parse("2021-11-09T10:30:00Z")
]
```

or use the bang functions:

```elixir
[
  DateTimeParser.parse_datetime!("2021-11-09T10:30:00Z"),
  DateTimeParser.parse_datetime!("2021-11-09T10:30:00"),
  DateTimeParser.parse_date!("2021-11-09T10:30:00Z"),
  DateTimeParser.parse_time!("2021-11-09T10:30:00Z"),
  DateTimeParser.parse!("2021-32-32T10:30:00Z"),
  DateTimeParser.parse!("2021-11-09T10:30:00Z")
]
```

Errors sometimes occur when it can't parse the input:

```elixir
[
  DateTimeParser.parse("wat"),
  DateTimeParser.parse(123),
  DateTimeParser.parse([:foo])
]
```

## Options

You can configure some convenient options as well, for example to automatically
convert to UTC or to assume a time when not present.

```elixir
[
  DateTimeParser.parse("12:30PM", assume_date: Date.utc_today()),
  DateTimeParser.parse("2022-01-01", assume_time: ~T[12:43:00]),
  DateTimeParser.parse("2022-01-01T15:30 EST", to_utc: true),
  DateTimeParser.parse("2022-01-01T15:30 EST", to_utc: false),
  # Excel time
  DateTimeParser.parse("30134"),
  # old Mac Excel spreadsheet time
  DateTimeParser.parse("30134.4321", use_1904_date_system: true)
]
```

## Examples

|**Input**|**Output (ISO 8601)**|**Method**|**Options**|
|:-------:|:-------------------:|:--------:|:---------:|
|` 01 Feb 2013`|`2013-02-01`|parse| |
|` 03 Jan 2013 10:15:26 -0800`|`2013-01-03T18:15:26Z`|parse|`[to_utc: true]`|
|` 10/1/2018  :: AM`|`2018-10-01`|parse| |
|` 11 Feb 2013`|`2013-02-11`|parse| |
|` 11 Jan 2013 13:26:55 -0800`|`2013-01-11T21:26:55Z`|parse|`[to_utc: true]`|
|` 12/26/2016`|`2016-12-26`|parse| |
|` 24 Sep 2013`|`2013-09-24`|parse| |
|`""=""9/5/2018"""`|`2018-09-05`|parse_date| |
|`""=""9/5/2018"""`|`2018-09-05T00:00:00`|parse_datetime|`[assume_time: true]`|
|`"=""10/1/2018"""`|`2018-10-01`|parse| |
|`"=""9/5/2018"""`|`2018-09-05`|parse| |
|`"Apr 1, 2016 12:02:53 AM PDT"`|`2016-04-01T07:02:53Z`|parse|`[to_utc: true]`|
|`"Apr 1, 2017 2:21:25 AM PDT"`|`2017-04-01T09:21:25Z`|parse|`[to_utc: true]`|
|`"Dec 1, 2018 7:39:53 AM PST"`|`2018-12-01T15:39:53Z`|parse|`[to_utc: true]`|
|`"Jan 1, 2013 06:34:31 PM PST"`|`2013-01-02T02:34:31Z`|parse|`[to_utc: true]`|
|`"Jan 1, 2014 6:44:47 AM PST"`|`2014-01-01T14:44:47Z`|parse|`[to_utc: true]`|
|`"Mar 28, 2014 6:44:47 AM PDT"`|`2014-03-28T13:44:47Z`|parse|`[to_utc: true]`|
|`"Nov 16, 2017 9:41:28 PM PST"`|`2017-11-17T05:41:28Z`|parse|`[to_utc: true]`|
|`"Nov 20, 2016 22:09:23 PM"`|`2016-11-20T22:09:23`|parse| |
|`"Sat, 29 Sep 2018 21:36:28 -0400"`|`2018-09-30T01:36:28Z`|parse|`[to_utc: true]`|
|`"September 28, 2016"`|`2016-09-28`|parse| |
|`"Tuesday, November 29, 2016"`|`2016-11-29`|parse| |
|`-0000000001`|`1969-12-31`|parse_date| |
|`-0000000001`|`1969-12-31T23:59:59Z`|parse_datetime| |
|`-0000000001.0000000001`|`23:59:58.000000`|parse_time| |
|`-0000000001.0000000001`|`1969-12-31T23:59:58.000000Z`|parse_datetime| |
|`-0000000001.000001`|`23:59:58.999999`|parse_time| |
|`-0000000001.00001`|`23:59:58.99999`|parse_time| |
|`-0000000001.0001`|`23:59:58.9999`|parse_time| |
|`-0000000001.001`|`1969-12-31`|parse_date| |
|`-0000000001.001`|`23:59:58.999`|parse_time| |
|`-0000000001.01`|`23:59:58.99`|parse_time| |
|`-0000000001.1`|`23:59:58.9`|parse_time| |
|`-0000000001.111111`|`1969-12-31`|parse_date| |
|`-0386380800`|`1957-10-04T00:00:00Z`|parse_datetime| |
|`-363`|`1899-01-01`|parse_date| |
|`-363.0`|`1899-01-01T00:00:00`|parse_datetime| |
|`-45103.1454398148`|`1776-07-04`|parse_date| |
|`-45103.1454398148`|`20:30:34`|parse_time| |
|`-45103.1454398148`|`1776-07-04T20:30:34`|parse_datetime| |
|`-9999999999`|`1653-02-10T06:13:21Z`|parse_datetime| |
|`-9999999999.009`|`1653-02-10`|parse_date| |
|`-9999999999.9`|`06:13:20.1`|parse_time| |
|`-9999999999.99`|`06:13:20.01`|parse_time| |
|`-9999999999.999`|`1653-02-10`|parse_date| |
|`-9999999999.999`|`06:13:20.001`|parse_time| |
|`-9999999999.9999`|`06:13:20.0001`|parse_time| |
|`-9999999999.99999`|`06:13:20.00001`|parse_time| |
|`-9999999999.999999`|`1653-02-10`|parse_date| |
|`-9999999999.999999`|`06:13:20.000001`|parse_time| |
|`-9999999999.9999999999`|`1653-02-10`|parse_date| |
|`-9999999999.9999999999`|`06:13:20.000001`|parse_time| |
|`-9999999999.9999999999`|`1653-02-10T06:13:20.000001Z`|parse_datetime| |
|`-99999999999`|`-1199-02-15T14:13:21Z`|parse_datetime| |
|`0000000000`|`1970-01-01`|parse_date| |
|`0000000000`|`00:00:00`|parse_time| |
|`0000000000`|`1970-01-01T00:00:00Z`|parse_datetime| |
|`00:00.0`|`00:00:00`|parse_time| |
|`01-01-2018`|`2018-01-01`|parse| |
|`01-Feb-18`|`2018-02-01`|parse| |
|`01-Jul`|`2019-07-01`|parse|`[assume_date: ~D[2019-01-05]]`|
|`01-Jul`|`Could not parse "01-Jul"`|parse| |
|`01-Jul`|`Could not parse "01-Jul"`|parse_datetime| |
|`01-Jul-18`|`2018-07-01`|parse| |
|`01.09.2018`|`2018-09-01`|parse| |
|`01.11.2018`|`2018-11-01`|parse| |
|`01/01/17`|`2017-01-01`|parse| |
|`01/01/2017`|`2017-01-01`|parse| |
|`01/01/2018 - 17:06`|`2018-01-01T17:06:00`|parse| |
|`01/01/2018 01:21PM`|`2018-01-01T13:21:00`|parse| |
|`01/01/2018 14:44`|`2018-01-01T14:44:00`|parse| |
|`01/01/2018 6:22`|`2018-01-01T06:22:00`|parse| |
|`01/02/16`|`2016-01-02`|parse| |
|`01/02/18 01:02 AM`|`2018-01-02T01:02:00`|parse| |
|`01/02/2015`|`2015-01-02`|parse| |
|`01/09/2034`|`2034-01-09`|parse_date| |
|`01/09/2034`|`2034-01-09T00:00:00`|parse_datetime|`[assume_time: true]`|
|`01/Jun./2018`|`2018-06-01`|parse| |
|`02-05-2018`|`2018-05-02`|parse| |
|`02-Oct-17`|`2017-10-02`|parse| |
|`02/01/17`|`2017-02-01`|parse| |
|`02/01/2018`|`2018-02-01`|parse| |
|`02/06/2019`|`2019-02-06`|parse_date| |
|`02/06/2019`|`2019-02-06T00:00:00`|parse_datetime|`[assume_time: true]`|
|`02/21/2018  9:37:42 AM`|`2018-02-21T09:37:42`|parse| |
|`03/5/2018`|`2018-03-05`|parse| |
|`05/01/2018 0:00`|`2018-05-01T00:00:00`|parse| |
|`06/14/2018 09:42:08 PM-0500`|`2018-06-15T02:42:08Z`|parse|`[to_utc: true]`|
|`06/28/18 1:25`|`2018-06-28T01:25:00`|parse| |
|`07:09.3`|`07:09:00`|parse_time| |
|`08:53.0`|`08:53:00`|parse_time| |
|`1-Apr`|`2019-04-01`|parse|`[assume_date: ~D[2019-01-13]]`|
|`1-Apr`|`Could not parse "1-Apr"`|parse| |
|`1//1/17`|`2017-01-01`|parse| |
|`1/1/0117`|`0117-01-01`|parse| |
|`1/1/17 19:12`|`2017-01-01T19:12:00`|parse| |
|`1/1/18 00:01`|`2018-01-01T00:01:00`|parse| |
|`1/1/18 3:24 PM`|`2018-01-01T15:24:00`|parse| |
|`1/1/19 10:39 AM`|`2019-01-01T10:39:00`|parse| |
|`1/1/2013`|`2013-01-01`|parse| |
|`1/10/2018  8:38pM`|`2018-01-10T20:38:00`|parse| |
|`1/13/19`|`2019-01-13`|parse_date| |
|`1/13/19`|`2019-01-13T00:00:00`|parse_datetime|`[assume_time: true]`|
|`1/13/2019`|`2019-01-13`|parse_date| |
|`1/13/2019`|`2019-01-13T00:00:00`|parse_datetime|`[assume_time: true]`|
|`1/15/2019 3:06`|`2019-01-15`|parse_date| |
|`1/15/2019 3:06`|`2019-01-15T03:06:00`|parse_datetime| |
|`1/17/2018 0:00:00`|`2018-01-17T00:00:00`|parse| |
|`1/2/2018 18:06:26`|`2018-01-02T18:06:26`|parse| |
|`1/3/2019 12:00:00 AM`|`2019-01-03T00:00:00`|parse| |
|`1/31/2018 0:00:00 UTC`|`2018-01-31T00:00:00Z`|parse| |
|`1/9/2034`|`2034-01-09`|parse_date| |
|`1/9/2034`|`2034-01-09T00:00:00`|parse_datetime|`[assume_time: true]`|
|`1/9/34`|`2034-01-09`|parse_date| |
|`1/9/34`|`2034-01-09T00:00:00`|parse_datetime|`[assume_time: true]`|
|`10/2/2017 - 23:14`|`2017-10-02T23:14:00`|parse| |
|`10/5/2017 23:52`|`2017-10-05T23:52:00`|parse| |
|`10:13.7`|`10:13:00`|parse_time| |
|`11 July 2017 1:43:46 PM`|`2017-07-11`|parse_date| |
|`11 July 2017 1:43:46 PM`|`2017-07-11T13:43:46`|parse_datetime| |
|`12/5`|`2021-12-05`|parse_date|`[assume_date: ~D[2021-01-01]]`|
|`12:30PM`|`12:30:00`|parse_time|`[assume_date: ~D[2020-01-01]]`|
|`12:30PM`|`2020-01-01T12:30:00`|parse|`[assume_date: ~D[2020-01-01]]`|
|`13/5`|`2021-05-13`|parse_date|`[assume_date: ~D[2021-01-01]]`|
|`18-07-2018 20:38:34 +00:00`|`2018-07-18T20:38:34Z`|parse| |
|`18-12-29`|`2018-12-29`|parse| |
|`19 September 18 2:33:08 PM`|`2018-09-19`|parse_date| |
|`19 September 18 2:33:08 PM`|`2018-09-19T14:33:08`|parse_datetime| |
|`19 September 2018 08:15:22 AM`|`2018-09-19`|parse_date| |
|`19 September 2018 08:15:22 AM`|`2018-09-19T08:15:22`|parse_datetime| |
|`19-Dec-19`|`2019-12-19`|parse| |
|`2`|`1900-01-01`|parse_date| |
|`2.0`|`1900-01-01T00:00:00`|parse_datetime| |
|`2/5`|`2021-02-05`|parse_date|`[assume_date: ~D[2021-01-01]]`|
|`2010/01/01`|`2010-01-01`|parse| |
|`2011-01-01 04:19:20 -0:00`|`2011-01-01T04:19:20Z`|parse| |
|`2012-10-30 09:52:00`|`2012-10-30T09:52:00`|parse| |
|`2012-11-23T22:42:25-05:00`|`2012-11-24T03:42:25Z`|parse|`[to_utc: true]`|
|`2013-04-26 11:25:03 UTC`|`2013-04-26T11:25:03Z`|parse| |
|`2013-09-10 22:14:56.717`|`2013-09-10T22:14:56.717`|parse| |
|`2013-12-31T22:18:50+00:00`|`2013-12-31T22:18:50Z`|parse| |
|`2015-09-28 10:57:11 -0700`|`2015-09-28T17:57:11Z`|parse|`[to_utc: true]`|
|`2015/12/1 1:16`|`2015-12-01T01:16:00`|parse| |
|`2016-02-29`|`2016-02-29`|parse_date| |
|`2016-02-29 00:00:00 UTC`|`2016-02-29T00:00:00Z`|parse_datetime| |
|`2016-04-30`|`2016-04-30`|parse| |
|`2016-05-02T01:10:06+00:00`|`2016-05-02T01:10:06Z`|parse| |
|`2016-06-11 15:50:43`|`2016-06-11T15:50:43`|parse| |
|`2016-06-16 06:06:06`|`2016-06-16T06:06:06`|parse| |
|`2016-07-01 01:51:34+00`|`2016-07-01T01:51:34Z`|parse| |
|`2016-07-31 18:42:46-07:00`|`2016-08-01T01:42:46Z`|parse|`[to_utc: true]`|
|`2016-08-04T07:00:25Z`|`2016-08-04T07:00:25Z`|parse| |
|`2016-08-19 09:34:51.0`|`2016-08-19T09:34:51.0`|parse| |
|`2016-11-17 10:36:34.81`|`2016-11-17T10:36:34.81`|parse| |
|`2016-11-23T16:25:33.971897`|`2016-11-23T16:25:33.971897`|parse| |
|`2016/1/9`|`2016-01-09`|parse| |
|`2017-02-29`|`Could not parse "2017-02-29"`|parse_date| |
|`2017-02-29 00:00:00 UTC`|`Could not parse "2017-02-29 00:00:00 UTC"`|parse_datetime| |
|`2017-03-04 15:20:47 EDT`|`2017-03-04T20:20:47Z`|parse_datetime|`[to_utc: true]`|
|`2017-03-04 15:20:47 EST`|`2017-03-04T20:20:47Z`|parse_datetime|`[to_utc: true]`|
|`2017-04-31`|`Could not parse "2017-04-31"`|parse_date| |
|`2017-04-31 00:00:00 UTC`|`Could not parse "2017-04-31 00:00:00 UTC"`|parse_datetime| |
|`2017-06-31`|`Could not parse "2017-06-31"`|parse_date| |
|`2017-06-31 00:00:00 UTC`|`Could not parse "2017-06-31 00:00:00 UTC"`|parse_datetime| |
|`2017-09-29+00:00`|`2017-09-29T00:00:00`|parse| |
|`2017-09-31`|`Could not parse "2017-09-31"`|parse_date| |
|`2017-09-31 00:00:00 UTC`|`Could not parse "2017-09-31 00:00:00 UTC"`|parse_datetime| |
|`2017-10-06+03:45:16`|`2017-10-06T03:45:16`|parse| |
|`2017-10-24 04:00:10 PDT`|`2017-10-24T11:00:10Z`|parse|`[to_utc: true]`|
|`2017-11-04 15:20:47 EDT`|`2017-11-04`|parse_date| |
|`2017-11-04 15:20:47 EDT`|`2017-11-04T19:20:47Z`|parse_datetime|`[to_utc: true]`|
|`2017-11-04 15:20:47 EST`|`2017-11-04`|parse_date| |
|`2017-11-04 15:20:47 EST`|`2017-11-04T19:20:47Z`|parse_datetime|`[to_utc: true]`|
|`2017-11-04 15:20:47 UTC`|`2017-11-04`|parse_date| |
|`2017-11-04 15:20:47 UTC`|`2017-11-04T15:20:47Z`|parse_datetime| |
|`2017-11-04 15:20:47+0000`|`2017-11-04`|parse_date| |
|`2017-11-04 15:20:47+0000`|`2017-11-04T15:20:47Z`|parse_datetime| |
|`2017-11-04 15:20:47+0500`|`2017-11-04`|parse_date| |
|`2017-11-04 15:20:47+0500`|`2017-11-04T10:20:47Z`|parse_datetime|`[to_utc: true]`|
|`2017-11-04 15:20:47-0500`|`2017-11-04`|parse_date| |
|`2017-11-04 15:20:47-0500`|`2017-11-04T20:20:47Z`|parse_datetime|`[to_utc: true]`|
|`2017-11-31`|`Could not parse "2017-11-31"`|parse_date| |
|`2017-11-31 00:00:00 UTC`|`Could not parse "2017-11-31 00:00:00 UTC"`|parse_datetime| |
|`2017-12-01 03:52`|`2017-12-01T03:52:00`|parse| |
|`2017/08/08`|`2017-08-08`|parse| |
|`2019-05-16+04:00`|`2019-05-16`|parse_date| |
|`2019-05-16+04:00`|`2019-05-16T04:00:00`|parse_datetime|`[assume_time: true]`|
|`2019-05-20 10:00:00PST`|`2019-05-20`|parse_date| |
|`2019-05-20 10:00:00PST`|`2019-05-20T17:00:00Z`|parse_datetime|`[to_utc: true]`|
|`2019/01/31 0:01`|`2019-01-31T00:01:00`|parse| |
|`2021-03-27 00:00 am`|`2021-03-27T00:00:00`|parse_datetime| |
|`2021-03-27 00:00 pm`|`2021-03-27T00:00:00`|parse_datetime| |
|`2021-03-27 12:00 am`|`2021-03-27T00:00:00`|parse_datetime| |
|`2021-03-27 12:00 pm`|`2021-03-27T12:00:00`|parse_datetime| |
|`2034-01-13`|`2034-01-13`|parse_date| |
|`2034-01-13`|`2034-01-13T00:00:00`|parse_datetime|`[assume_time: true]`|
|`2034-1-9`|`2034-01-09`|parse_date| |
|`2034-1-9`|`2034-01-09T00:00:00`|parse_datetime|`[assume_time: true]`|
|`20340109`|`2034-01-09T00:00:00`|parse_datetime|`[assume_time: true]`|
|`23-05-2019 @ 10:01`|`2019-05-23`|parse_date| |
|`23-05-2019 @ 10:01`|`2019-05-23T10:01:00`|parse_datetime|`[assume_time: true]`|
|`24:00`|`Could not parse "24:00"`|parse_time| |
|`29/Aug./2018`|`2018-08-29`|parse| |
|`29/Sep./2018`|`2018-09-29`|parse| |
|`30134`|`1982-07-02`|parse| |
|`30134.0`|`1982-07-02T00:00:00`|parse| |
|`34-1-13`|`2034-01-13`|parse_date| |
|`34-1-13`|`2034-01-13T00:00:00`|parse_datetime|`[assume_time: true]`|
|`4/24/2019 0:00:00`|`2019-04-24`|parse_date| |
|`4/24/2019 0:00:00`|`2019-04-24T00:00:00`|parse_datetime| |
|`41261.6013888889`|`2012-12-18`|parse_date| |
|`41261.6013888889`|`14:26:00`|parse_time| |
|`41261.6013888889`|`2012-12-18T14:26:00`|parse_datetime| |
|`5/12/2019 12:21:58 PM`|`2019-05-12T12:21:58`|parse| |
|`5/2/2019 0:00:00`|`2019-05-02`|parse_date| |
|`5/2/2019 0:00:00`|`2019-05-02T00:00:00`|parse_datetime| |
|`5/2/2019 12:00:00 AM`|`2019-05-02`|parse_date| |
|`5/2/2019 12:00:00 AM`|`2019-05-02T00:00:00`|parse_datetime| |
|`5/31/2019 12:00:00 AM`|`2019-05-31`|parse_date| |
|`5/31/2019 12:00:00 AM`|`2019-05-31T00:00:00`|parse_datetime| |
|`62`|`1900-03-02`|parse_date| |
|`62`|`1900-03-02T00:00:00`|parse|`[assume_time: true]`|
|`62`|`1904-03-03T00:00:00`|parse|`[assume_time: true, use_1904_date_system: true]`|
|`62.0`|`1900-03-02T00:00:00`|parse| |
|`62.0`|`1900-03-02T00:00:00`|parse_datetime| |
|`62.0`|`1904-03-03T00:00:00`|parse|`[use_1904_date_system: true]`|
|`62.0`|`1904-03-03T00:00:00`|parse_datetime|`[use_1904_date_system: true]`|
|`9-2-32`|`2032-02-09`|parse_date| |
|`9-2-32`|`2032-02-09T00:00:00`|parse_datetime|`[assume_time: true]`|
|`9-Feb-18`|`2018-02-09`|parse_date| |
|`9-Feb-18`|`2018-02-09T00:00:00`|parse_datetime|`[assume_time: true]`|
|`9/1/2018 10:26`|`2018-09-01`|parse_date| |
|`9/1/2018 10:26`|`2018-09-01T10:26:00`|parse_datetime|`[assume_time: true]`|
|`9/10/2018 11:08:13 AM`|`2018-09-10T11:08:13`|parse| |
|`9/19/2018 20:38`|`2018-09-19T20:38:00`|parse| |
|`9/20/2017 18:57:24 UTC`|`2017-09-20T18:57:24Z`|parse| |
|`9/4/2018 0:00`|`2018-09-04`|parse_date| |
|`9/4/2018 0:00`|`2018-09-04T00:00:00`|parse_datetime| |
|`9999999999`|`2286-11-20`|parse_date| |
|`9999999999`|`17:46:39`|parse_time| |
|`9999999999`|`2286-11-20T17:46:39Z`|parse_datetime| |
|`9999999999.0000000009`|`2286-11-20T17:46:39.000000Z`|parse_datetime| |
|`9999999999.0000009000`|`2286-11-20T17:46:39.000000Z`|parse_datetime| |
|`9999999999.000001`|`17:46:39.000001`|parse_time| |
|`9999999999.000010`|`17:46:39.000010`|parse_time| |
|`9999999999.000100`|`17:46:39.000100`|parse_time| |
|`9999999999.001000`|`17:46:39.001000`|parse_time| |
|`9999999999.009`|`2286-11-20`|parse_date| |
|`9999999999.009`|`17:46:39.009`|parse_time| |
|`9999999999.009`|`2286-11-20T17:46:39.009Z`|parse_datetime| |
|`9999999999.010000`|`17:46:39.010000`|parse_time| |
|`9999999999.090`|`2286-11-20T17:46:39.090Z`|parse_datetime| |
|`9999999999.100000`|`17:46:39.100000`|parse_time| |
|`9999999999.900`|`17:46:39.900`|parse_time| |
|`9999999999.900`|`2286-11-20T17:46:39.900Z`|parse_datetime| |
|`9999999999.999`|`2286-11-20`|parse_date| |
|`9999999999.999`|`17:46:39.999`|parse_time| |
|`9999999999.999`|`2286-11-20T17:46:39.999Z`|parse_datetime| |
|`9999999999.999999`|`2286-11-20`|parse_date| |
|`9999999999.999999`|`17:46:39.999999`|parse_time| |
|`9999999999.999999`|`2286-11-20T17:46:39.999999Z`|parse_datetime| |
|`9999999999.9999999999`|`2286-11-20`|parse_date| |
|`9999999999.9999999999`|`17:46:39.999999`|parse_time| |
|`9999999999.9999999999`|`2286-11-20T17:46:39.999999Z`|parse_datetime| |
|`99999999999`|`5138-11-16`|parse_date| |
|`99999999999`|`09:46:39`|parse_time| |
|`99999999999`|`5138-11-16T09:46:39Z`|parse_datetime| |
|`Fri Mar  2 09:01:57 2018`|`2018-03-02T09:01:57`|parse| |
|`Fri Mar 31 2017 21:41:40 GMT+0000 (UTC)`|`2017-03-31T21:41:40Z`|parse| |
|`Friday 02 February 2018 10:42:21 AM`|`2018-02-02T10:42:21`|parse| |
|`Jan 2020`|`2020-01-01`|parse_date|`[assume_date: ~D[0001-12-01]]`|
|`Jan-01-19`|`2019-01-01`|parse| |
|`Jan-01-19`|`2019-01-01T00:00:00`|parse|`[assume_time: true]`|
|`Jan-01-19`|`2019-01-01T10:13:15`|parse|`[assume_time: ~T[10:13:15]]`|
|`Jan-01-2018`|`2018-01-01`|parse| |
|`May 1442`|`1442-05-01`|parse_date|`[assume_date: ~D[0001-12-01]]`|
|`May 30, 2019 4:31:09 AM PDT`|`2019-05-30`|parse_date| |
|`May 30, 2019 4:31:09 AM PDT`|`2019-05-30T11:31:09Z`|parse_datetime|`[to_utc: true]`|
|`Monday 01 October 2018 06:34:19 AM`|`2018-10-01T06:34:19`|parse| |
|`Monday 02 October 2017 9:04:49 AM`|`2017-10-02T09:04:49`|parse| |
|`November 29, 2016`|`2016-11-29`|parse_date| |
|`November 29, 2016`|`2016-11-29T00:00:00`|parse_datetime|`[assume_time: true]`|
|`Oct 5, 2018 6:16:56 PM PDT`|`2018-10-05`|parse_date| |
|`Oct 5, 2018 6:16:56 PM PDT`|`2018-10-06T01:16:56Z`|parse_datetime|`[to_utc: true]`|
|`October 1995`|`1995-10-01`|parse_date|`[assume_date: ~D[0001-12-01]]`|
|`Sep-19-16`|`2016-09-19`|parse_date| |
|`Sep-19-16`|`2016-09-19T00:00:00`|parse_datetime|`[assume_time: true]`|
|`Sun 01 January 2017 10:11:02 PM`|`2017-01-01`|parse_date| |
|`Sun 01 January 2017 10:11:02 PM`|`2017-01-01T22:11:02`|parse_datetime| |
|`Sun Jan 08 2017 04:28:42 GMT+0000 (UTC)`|`2017-01-08T04:28:42Z`|parse| |
|`Sun Jul  1 00:31:18 2018`|`2018-07-01T00:31:18`|parse| |
|`Sun, 01 January 2017 10:11:02 PM`|`2017-01-01`|parse_date| |
|`Sun, 01 January 2017 10:11:02 PM`|`2017-01-01T22:11:02`|parse_datetime| |
|`Sunday 01 January 2017 09:22:46 AM`|`2017-01-01T09:22:46`|parse| |
|`Sunday 01 January 2017 10:11:02 PM`|`2017-01-01`|parse_date| |
|`Sunday 01 January 2017 10:11:02 PM`|`2017-01-01T22:11:02`|parse_datetime| |
|`Sunday 01 January 2017 10:11:02 PM`|`2017-01-01T22:11:02`|parse| |
|`Sunday, 01 January 2017 10:11:02 PM`|`2017-01-01`|parse_date| |
|`Sunday, 01 January 2017 10:11:02 PM`|`2017-01-01T22:11:02`|parse_datetime| |
|`Thu Aug 09 2018 17:13:43 GMT+0000 (UTC)`|`2018-08-09T17:13:43Z`|parse| |
|`Thu Feb 08 00:24:33 2018`|`2018-02-08T00:24:33`|parse| |
|`Thu Jul  5 12:19:56 2018`|`2018-07-05T12:19:56`|parse| |
|`Thursday 30 August 2018 11:31:18 AM`|`2018-08-30T11:31:18`|parse| |
|`Tue Jul 31 06:44:39 2018`|`2018-07-31T06:44:39`|parse| |
|`Tuesday 11 July 2017 1:43:46 PM`|`2017-07-11T13:43:46`|parse| |
|`jul-10-18`|`2018-07-10`|parse| |
