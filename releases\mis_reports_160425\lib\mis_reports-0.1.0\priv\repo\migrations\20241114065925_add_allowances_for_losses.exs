defmodule MisReports.Repo.Migrations.AddAllowancesForLosses do
  use Ecto.Migration

  def up do
    alter table(:tblm_allowances) do
      add :curr_allowance_for_loan_losses_stg_three, :decimal, precision: 18, scale: 2
      add :pre_allowance_for_loan_losses_stg_three, :decimal, precision: 18, scale: 2
    end
  end

  def down do
    alter table(:tblm_allowances) do
      remove :curr_allowance_for_loan_losses_stg_three
      remove :pre_allowance_for_loan_losses_stg_three
    end
  end
end
