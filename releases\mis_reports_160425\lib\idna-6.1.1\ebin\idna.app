{application,idna,
             [{description,"A pure Erlang IDNA implementation"},
              {vsn,"6.1.1"},
              {modules,[idna,idna_bidi,idna_context,idna_data,idna_mapping,
                        idna_table,idna_ucs,punycode]},
              {registered,[]},
              {applications,[kernel,stdlib,unicode_util_compat]},
              {licenses,["MIT"]},
              {links,[{"Github","https://github.com/benoitc/erlang-idna"}]}]}.
