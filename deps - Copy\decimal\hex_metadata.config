{<<"app">>,<<"decimal">>}.
{<<"build_tools">>,[<<"mix">>]}.
{<<"description">>,<<"Arbitrary precision decimal arithmetic.">>}.
{<<"elixir">>,<<"~> 1.2">>}.
{<<"files">>,
 [<<"lib">>,<<"lib/decimal">>,<<"lib/decimal/error.ex">>,
  <<"lib/decimal/context.ex">>,<<"lib/decimal/macros.ex">>,
  <<"lib/decimal.ex">>,<<".formatter.exs">>,<<"mix.exs">>,<<"README.md">>,
  <<"LICENSE.txt">>,<<"CHANGELOG.md">>]}.
{<<"licenses">>,[<<"Apache-2.0">>]}.
{<<"links">>,[{<<"GitHub">>,<<"https://github.com/ericmj/decimal">>}]}.
{<<"name">>,<<"decimal">>}.
{<<"requirements">>,[]}.
{<<"version">>,<<"2.0.0">>}.
