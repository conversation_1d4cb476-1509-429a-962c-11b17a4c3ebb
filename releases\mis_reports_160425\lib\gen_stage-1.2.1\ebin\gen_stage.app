{application,gen_stage,
             [{applications,[kernel,stdlib,elixir,logger]},
              {description,"Producer and consumer actors with back-pressure for Elixir"},
              {modules,['Elixir.ConsumerSupervisor',
                        'Elixir.ConsumerSupervisor.Default','Elixir.GenStage',
                        'Elixir.GenStage.BroadcastDispatcher',
                        'Elixir.GenStage.Buffer',
                        'Elixir.GenStage.DemandDispatcher',
                        'Elixir.GenStage.Dispatcher',
                        'Elixir.GenStage.PartitionDispatcher',
                        'Elixir.GenStage.Stream','Elixir.GenStage.Streamer',
                        'Elixir.GenStage.Utils']},
              {registered,[]},
              {vsn,"1.2.1"}]}.
