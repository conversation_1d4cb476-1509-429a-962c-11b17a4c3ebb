defmodule MisReports.Repo.Migrations.CreateWklspecialRules do
  use Ecto.Migration

  def change do
    create table(:wklspecial_rules) do
      add :special_rule_id, :integer
      add :process_id, :integer
      add :step_id, :integer
      add :functional_office_id, :integer
      add :action_id, :integer
      add :user_id, :string
      add :rule_code, :string
      add :created_by, :string
      add :created_date, :date
      add :modified_by, :string
      add :modified_date, :date

      timestamps()
    end
  end
end
