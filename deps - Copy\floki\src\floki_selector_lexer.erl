-file("c:/Program Files/Erlang OTP/lib/parsetools-2.4.1/include/leexinc.hrl", 0).
%% The source of this file is part of leex distribution, as such it
%% has the same Copyright as the other files in the leex
%% distribution. The Copyright is defined in the accompanying file
%% COPYRIGHT. However, the resultant scanner generated by leex is the
%% property of the creator of the scanner and is not covered by that
%% Copyright.

-module(floki_selector_lexer).

-export([string/1,string/2,token/2,token/3,tokens/2,tokens/3]).
-export([format_error/1]).

%% User code. This is placed here to allow extra attributes.
-file("src/floki_selector_lexer.xrl", 46).

remove_wrapper(Chars) ->
  Len = string:len(Chars),
  string:substr(Chars, 2, Len - 2).

tail([_|T]) ->
  T.

unescape_inside_class_name(Chars) ->
  lists:flatten(string:replace(Chars, "\\:", ":", all)).

unescape_inside_id_name(Chars) ->
  lists:flatten(string:replace(Chars, "\\.", ".", all)).

-file("c:/Program Files/Erlang OTP/lib/parsetools-2.4.1/include/leexinc.hrl", 14).

format_error({illegal,S}) -> ["illegal characters ",io_lib:write_string(S)];
format_error({user,S}) -> S.

string(String) -> string(String, 1).

string(String, Line) -> string(String, Line, String, []).

%% string(InChars, Line, TokenChars, Tokens) ->
%% {ok,Tokens,Line} | {error,ErrorInfo,Line}.
%% Note the line number going into yystate, L0, is line of token
%% start while line number returned is line of token end. We want line
%% of token start.

string([], L, [], Ts) ->                     % No partial tokens!
    {ok,yyrev(Ts),L};
string(Ics0, L0, Tcs, Ts) ->
    case yystate(yystate(), Ics0, L0, 0, reject, 0) of
        {A,Alen,Ics1,L1} ->                  % Accepting end state
            string_cont(Ics1, L1, yyaction(A, Alen, Tcs, L0), Ts);
        {A,Alen,Ics1,L1,_S1} ->              % Accepting transition state
            string_cont(Ics1, L1, yyaction(A, Alen, Tcs, L0), Ts);
        {reject,_Alen,Tlen,_Ics1,L1,_S1} ->  % After a non-accepting state
            {error,{L0,?MODULE,{illegal,yypre(Tcs, Tlen+1)}},L1};
        {A,Alen,Tlen,_Ics1,L1,_S1} ->
            Tcs1 = yysuf(Tcs, Alen),
            L2 = adjust_line(Tlen, Alen, Tcs1, L1),
            string_cont(Tcs1, L2, yyaction(A, Alen, Tcs, L0), Ts)
    end.

%% string_cont(RestChars, Line, Token, Tokens)
%% Test for and remove the end token wrapper. Push back characters
%% are prepended to RestChars.

-dialyzer({nowarn_function, string_cont/4}).

string_cont(Rest, Line, {token,T}, Ts) ->
    string(Rest, Line, Rest, [T|Ts]);
string_cont(Rest, Line, {token,T,Push}, Ts) ->
    NewRest = Push ++ Rest,
    string(NewRest, Line, NewRest, [T|Ts]);
string_cont(Rest, Line, {end_token,T}, Ts) ->
    string(Rest, Line, Rest, [T|Ts]);
string_cont(Rest, Line, {end_token,T,Push}, Ts) ->
    NewRest = Push ++ Rest,
    string(NewRest, Line, NewRest, [T|Ts]);
string_cont(Rest, Line, skip_token, Ts) ->
    string(Rest, Line, Rest, Ts);
string_cont(Rest, Line, {skip_token,Push}, Ts) ->
    NewRest = Push ++ Rest,
    string(NewRest, Line, NewRest, Ts);
string_cont(_Rest, Line, {error,S}, _Ts) ->
    {error,{Line,?MODULE,{user,S}},Line}.

%% token(Continuation, Chars) ->
%% token(Continuation, Chars, Line) ->
%% {more,Continuation} | {done,ReturnVal,RestChars}.
%% Must be careful when re-entering to append the latest characters to the
%% after characters in an accept. The continuation is:
%% {token,State,CurrLine,TokenChars,TokenLen,TokenLine,AccAction,AccLen}

token(Cont, Chars) -> token(Cont, Chars, 1).

token([], Chars, Line) ->
    token(yystate(), Chars, Line, Chars, 0, Line, reject, 0);
token({token,State,Line,Tcs,Tlen,Tline,Action,Alen}, Chars, _) ->
    token(State, Chars, Line, Tcs ++ Chars, Tlen, Tline, Action, Alen).

%% token(State, InChars, Line, TokenChars, TokenLen, TokenLine,
%% AcceptAction, AcceptLen) ->
%% {more,Continuation} | {done,ReturnVal,RestChars}.
%% The argument order is chosen to be more efficient.

token(S0, Ics0, L0, Tcs, Tlen0, Tline, A0, Alen0) ->
    case yystate(S0, Ics0, L0, Tlen0, A0, Alen0) of
        %% Accepting end state, we have a token.
        {A1,Alen1,Ics1,L1} ->
            token_cont(Ics1, L1, yyaction(A1, Alen1, Tcs, Tline));
        %% Accepting transition state, can take more chars.
        {A1,Alen1,[],L1,S1} ->                  % Need more chars to check
            {more,{token,S1,L1,Tcs,Alen1,Tline,A1,Alen1}};
        {A1,Alen1,Ics1,L1,_S1} ->               % Take what we got
            token_cont(Ics1, L1, yyaction(A1, Alen1, Tcs, Tline));
        %% After a non-accepting state, maybe reach accept state later.
        {A1,Alen1,Tlen1,[],L1,S1} ->            % Need more chars to check
            {more,{token,S1,L1,Tcs,Tlen1,Tline,A1,Alen1}};
        {reject,_Alen1,Tlen1,eof,L1,_S1} ->     % No token match
            %% Check for partial token which is error.
            Ret = if Tlen1 > 0 -> {error,{Tline,?MODULE,
                                          %% Skip eof tail in Tcs.
                                          {illegal,yypre(Tcs, Tlen1)}},L1};
                     true -> {eof,L1}
                  end,
            {done,Ret,eof};
        {reject,_Alen1,Tlen1,Ics1,L1,_S1} ->    % No token match
            Error = {Tline,?MODULE,{illegal,yypre(Tcs, Tlen1+1)}},
            {done,{error,Error,L1},Ics1};
        {A1,Alen1,Tlen1,_Ics1,L1,_S1} ->       % Use last accept match
            Tcs1 = yysuf(Tcs, Alen1),
            L2 = adjust_line(Tlen1, Alen1, Tcs1, L1),
            token_cont(Tcs1, L2, yyaction(A1, Alen1, Tcs, Tline))
    end.

%% token_cont(RestChars, Line, Token)
%% If we have a token or error then return done, else if we have a
%% skip_token then continue.

-dialyzer({nowarn_function, token_cont/3}).

token_cont(Rest, Line, {token,T}) ->
    {done,{ok,T,Line},Rest};
token_cont(Rest, Line, {token,T,Push}) ->
    NewRest = Push ++ Rest,
    {done,{ok,T,Line},NewRest};
token_cont(Rest, Line, {end_token,T}) ->
    {done,{ok,T,Line},Rest};
token_cont(Rest, Line, {end_token,T,Push}) ->
    NewRest = Push ++ Rest,
    {done,{ok,T,Line},NewRest};
token_cont(Rest, Line, skip_token) ->
    token(yystate(), Rest, Line, Rest, 0, Line, reject, 0);
token_cont(Rest, Line, {skip_token,Push}) ->
    NewRest = Push ++ Rest,
    token(yystate(), NewRest, Line, NewRest, 0, Line, reject, 0);
token_cont(Rest, Line, {error,S}) ->
    {done,{error,{Line,?MODULE,{user,S}},Line},Rest}.

%% tokens(Continuation, Chars, Line) ->
%% {more,Continuation} | {done,ReturnVal,RestChars}.
%% Must be careful when re-entering to append the latest characters to the
%% after characters in an accept. The continuation is:
%% {tokens,State,CurrLine,TokenChars,TokenLen,TokenLine,Tokens,AccAction,AccLen}
%% {skip_tokens,State,CurrLine,TokenChars,TokenLen,TokenLine,Error,AccAction,AccLen}

tokens(Cont, Chars) -> tokens(Cont, Chars, 1).

tokens([], Chars, Line) ->
    tokens(yystate(), Chars, Line, Chars, 0, Line, [], reject, 0);
tokens({tokens,State,Line,Tcs,Tlen,Tline,Ts,Action,Alen}, Chars, _) ->
    tokens(State, Chars, Line, Tcs ++ Chars, Tlen, Tline, Ts, Action, Alen);
tokens({skip_tokens,State,Line,Tcs,Tlen,Tline,Error,Action,Alen}, Chars, _) ->
    skip_tokens(State, Chars, Line, Tcs ++ Chars, Tlen, Tline, Error, Action, Alen).

%% tokens(State, InChars, Line, TokenChars, TokenLen, TokenLine, Tokens,
%% AcceptAction, AcceptLen) ->
%% {more,Continuation} | {done,ReturnVal,RestChars}.

tokens(S0, Ics0, L0, Tcs, Tlen0, Tline, Ts, A0, Alen0) ->
    case yystate(S0, Ics0, L0, Tlen0, A0, Alen0) of
        %% Accepting end state, we have a token.
        {A1,Alen1,Ics1,L1} ->
            tokens_cont(Ics1, L1, yyaction(A1, Alen1, Tcs, Tline), Ts);
        %% Accepting transition state, can take more chars.
        {A1,Alen1,[],L1,S1} ->                  % Need more chars to check
            {more,{tokens,S1,L1,Tcs,Alen1,Tline,Ts,A1,Alen1}};
        {A1,Alen1,Ics1,L1,_S1} ->               % Take what we got
            tokens_cont(Ics1, L1, yyaction(A1, Alen1, Tcs, Tline), Ts);
        %% After a non-accepting state, maybe reach accept state later.
        {A1,Alen1,Tlen1,[],L1,S1} ->            % Need more chars to check
            {more,{tokens,S1,L1,Tcs,Tlen1,Tline,Ts,A1,Alen1}};
        {reject,_Alen1,Tlen1,eof,L1,_S1} ->     % No token match
            %% Check for partial token which is error, no need to skip here.
            Ret = if Tlen1 > 0 -> {error,{Tline,?MODULE,
                                          %% Skip eof tail in Tcs.
                                          {illegal,yypre(Tcs, Tlen1)}},L1};
                     Ts == [] -> {eof,L1};
                     true -> {ok,yyrev(Ts),L1}
                  end,
            {done,Ret,eof};
        {reject,_Alen1,Tlen1,_Ics1,L1,_S1} ->
            %% Skip rest of tokens.
            Error = {L1,?MODULE,{illegal,yypre(Tcs, Tlen1+1)}},
            skip_tokens(yysuf(Tcs, Tlen1+1), L1, Error);
        {A1,Alen1,Tlen1,_Ics1,L1,_S1} ->
            Token = yyaction(A1, Alen1, Tcs, Tline),
            Tcs1 = yysuf(Tcs, Alen1),
            L2 = adjust_line(Tlen1, Alen1, Tcs1, L1),
            tokens_cont(Tcs1, L2, Token, Ts)
    end.

%% tokens_cont(RestChars, Line, Token, Tokens)
%% If we have an end_token or error then return done, else if we have
%% a token then save it and continue, else if we have a skip_token
%% just continue.

-dialyzer({nowarn_function, tokens_cont/4}).

tokens_cont(Rest, Line, {token,T}, Ts) ->
    tokens(yystate(), Rest, Line, Rest, 0, Line, [T|Ts], reject, 0);
tokens_cont(Rest, Line, {token,T,Push}, Ts) ->
    NewRest = Push ++ Rest,
    tokens(yystate(), NewRest, Line, NewRest, 0, Line, [T|Ts], reject, 0);
tokens_cont(Rest, Line, {end_token,T}, Ts) ->
    {done,{ok,yyrev(Ts, [T]),Line},Rest};
tokens_cont(Rest, Line, {end_token,T,Push}, Ts) ->
    NewRest = Push ++ Rest,
    {done,{ok,yyrev(Ts, [T]),Line},NewRest};
tokens_cont(Rest, Line, skip_token, Ts) ->
    tokens(yystate(), Rest, Line, Rest, 0, Line, Ts, reject, 0);
tokens_cont(Rest, Line, {skip_token,Push}, Ts) ->
    NewRest = Push ++ Rest,
    tokens(yystate(), NewRest, Line, NewRest, 0, Line, Ts, reject, 0);
tokens_cont(Rest, Line, {error,S}, _Ts) ->
    skip_tokens(Rest, Line, {Line,?MODULE,{user,S}}).

%%skip_tokens(InChars, Line, Error) -> {done,{error,Error,Line},Ics}.
%% Skip tokens until an end token, junk everything and return the error.

skip_tokens(Ics, Line, Error) ->
    skip_tokens(yystate(), Ics, Line, Ics, 0, Line, Error, reject, 0).

%% skip_tokens(State, InChars, Line, TokenChars, TokenLen, TokenLine, Tokens,
%% AcceptAction, AcceptLen) ->
%% {more,Continuation} | {done,ReturnVal,RestChars}.

skip_tokens(S0, Ics0, L0, Tcs, Tlen0, Tline, Error, A0, Alen0) ->
    case yystate(S0, Ics0, L0, Tlen0, A0, Alen0) of
        {A1,Alen1,Ics1,L1} ->                  % Accepting end state
            skip_cont(Ics1, L1, yyaction(A1, Alen1, Tcs, Tline), Error);
        {A1,Alen1,[],L1,S1} ->                 % After an accepting state
            {more,{skip_tokens,S1,L1,Tcs,Alen1,Tline,Error,A1,Alen1}};
        {A1,Alen1,Ics1,L1,_S1} ->
            skip_cont(Ics1, L1, yyaction(A1, Alen1, Tcs, Tline), Error);
        {A1,Alen1,Tlen1,[],L1,S1} ->           % After a non-accepting state
            {more,{skip_tokens,S1,L1,Tcs,Tlen1,Tline,Error,A1,Alen1}};
        {reject,_Alen1,_Tlen1,eof,L1,_S1} ->
            {done,{error,Error,L1},eof};
        {reject,_Alen1,Tlen1,_Ics1,L1,_S1} ->
            skip_tokens(yysuf(Tcs, Tlen1+1), L1, Error);
        {A1,Alen1,Tlen1,_Ics1,L1,_S1} ->
            Token = yyaction(A1, Alen1, Tcs, Tline),
            Tcs1 = yysuf(Tcs, Alen1),
            L2 = adjust_line(Tlen1, Alen1, Tcs1, L1),
            skip_cont(Tcs1, L2, Token, Error)
    end.

%% skip_cont(RestChars, Line, Token, Error)
%% Skip tokens until we have an end_token or error then return done
%% with the original rror.

-dialyzer({nowarn_function, skip_cont/4}).

skip_cont(Rest, Line, {token,_T}, Error) ->
    skip_tokens(yystate(), Rest, Line, Rest, 0, Line, Error, reject, 0);
skip_cont(Rest, Line, {token,_T,Push}, Error) ->
    NewRest = Push ++ Rest,
    skip_tokens(yystate(), NewRest, Line, NewRest, 0, Line, Error, reject, 0);
skip_cont(Rest, Line, {end_token,_T}, Error) ->
    {done,{error,Error,Line},Rest};
skip_cont(Rest, Line, {end_token,_T,Push}, Error) ->
    NewRest = Push ++ Rest,
    {done,{error,Error,Line},NewRest};
skip_cont(Rest, Line, skip_token, Error) ->
    skip_tokens(yystate(), Rest, Line, Rest, 0, Line, Error, reject, 0);
skip_cont(Rest, Line, {skip_token,Push}, Error) ->
    NewRest = Push ++ Rest,
    skip_tokens(yystate(), NewRest, Line, NewRest, 0, Line, Error, reject, 0);
skip_cont(Rest, Line, {error,_S}, Error) ->
    skip_tokens(yystate(), Rest, Line, Rest, 0, Line, Error, reject, 0).

-compile({nowarn_unused_function, [yyrev/1, yyrev/2, yypre/2, yysuf/2]}).

yyrev(List) -> lists:reverse(List).
yyrev(List, Tail) -> lists:reverse(List, Tail).
yypre(List, N) -> lists:sublist(List, N).
yysuf(List, N) -> lists:nthtail(N, List).

%% adjust_line(TokenLength, AcceptLength, Chars, Line) -> NewLine
%% Make sure that newlines in Chars are not counted twice.
%% Line has been updated with respect to newlines in the prefix of
%% Chars consisting of (TokenLength - AcceptLength) characters.

-compile({nowarn_unused_function, adjust_line/4}).

adjust_line(N, N, _Cs, L) -> L;
adjust_line(T, A, [$\n|Cs], L) ->
    adjust_line(T-1, A, Cs, L-1);
adjust_line(T, A, [_|Cs], L) ->
    adjust_line(T-1, A, Cs, L).

%% yystate() -> InitialState.
%% yystate(State, InChars, Line, CurrTokLen, AcceptAction, AcceptLen) ->
%% {Action, AcceptLen, RestChars, Line} |
%% {Action, AcceptLen, RestChars, Line, State} |
%% {reject, AcceptLen, CurrTokLen, RestChars, Line, State} |
%% {Action, AcceptLen, CurrTokLen, RestChars, Line, State}.
%% Generated state transition functions. The non-accepting end state
%% return signal either an unrecognised character or end of current
%% input.

-file("src/floki_selector_lexer.erl", 320).
yystate() -> 69.

yystate(72, [41|Ics], Line, Tlen, Action, Alen) ->
    yystate(68, Ics, Line, Tlen+1, Action, Alen);
yystate(72, [C|Ics], Line, Tlen, Action, Alen) when C >= 48, C =< 57 ->
    yystate(72, Ics, Line, Tlen+1, Action, Alen);
yystate(72, Ics, Line, Tlen, Action, Alen) ->
    {Action,Alen,Tlen,Ics,Line,72};
yystate(71, Ics, Line, Tlen, _, _) ->
    {26,Tlen,Ics,Line};
yystate(70, [C|Ics], Line, Tlen, Action, Alen) when C >= 48, C =< 57 ->
    yystate(72, Ics, Line, Tlen+1, Action, Alen);
yystate(70, Ics, Line, Tlen, Action, Alen) ->
    {Action,Alen,Tlen,Ics,Line,70};
yystate(69, [126|Ics], Line, Tlen, Action, Alen) ->
    yystate(65, Ics, Line, Tlen+1, Action, Alen);
yystate(69, [125|Ics], Line, Tlen, Action, Alen) ->
    yystate(71, Ics, Line, Tlen+1, Action, Alen);
yystate(69, [124|Ics], Line, Tlen, Action, Alen) ->
    yystate(53, Ics, Line, Tlen+1, Action, Alen);
yystate(69, [123|Ics], Line, Tlen, Action, Alen) ->
    yystate(71, Ics, Line, Tlen+1, Action, Alen);
yystate(69, [96|Ics], Line, Tlen, Action, Alen) ->
    yystate(71, Ics, Line, Tlen+1, Action, Alen);
yystate(69, [95|Ics], Line, Tlen, Action, Alen) ->
    yystate(30, Ics, Line, Tlen+1, Action, Alen);
yystate(69, [94|Ics], Line, Tlen, Action, Alen) ->
    yystate(41, Ics, Line, Tlen+1, Action, Alen);
yystate(69, [93|Ics], Line, Tlen, Action, Alen) ->
    yystate(33, Ics, Line, Tlen+1, Action, Alen);
yystate(69, [92|Ics], Line, Tlen, Action, Alen) ->
    yystate(71, Ics, Line, Tlen+1, Action, Alen);
yystate(69, [91|Ics], Line, Tlen, Action, Alen) ->
    yystate(33, Ics, Line, Tlen+1, Action, Alen);
yystate(69, [63|Ics], Line, Tlen, Action, Alen) ->
    yystate(71, Ics, Line, Tlen+1, Action, Alen);
yystate(69, [64|Ics], Line, Tlen, Action, Alen) ->
    yystate(71, Ics, Line, Tlen+1, Action, Alen);
yystate(69, [62|Ics], Line, Tlen, Action, Alen) ->
    yystate(51, Ics, Line, Tlen+1, Action, Alen);
yystate(69, [61|Ics], Line, Tlen, Action, Alen) ->
    yystate(29, Ics, Line, Tlen+1, Action, Alen);
yystate(69, [59|Ics], Line, Tlen, Action, Alen) ->
    yystate(71, Ics, Line, Tlen+1, Action, Alen);
yystate(69, [60|Ics], Line, Tlen, Action, Alen) ->
    yystate(71, Ics, Line, Tlen+1, Action, Alen);
yystate(69, [58|Ics], Line, Tlen, Action, Alen) ->
    yystate(25, Ics, Line, Tlen+1, Action, Alen);
yystate(69, [47|Ics], Line, Tlen, Action, Alen) ->
    yystate(71, Ics, Line, Tlen+1, Action, Alen);
yystate(69, [46|Ics], Line, Tlen, Action, Alen) ->
    yystate(10, Ics, Line, Tlen+1, Action, Alen);
yystate(69, [45|Ics], Line, Tlen, Action, Alen) ->
    yystate(30, Ics, Line, Tlen+1, Action, Alen);
yystate(69, [44|Ics], Line, Tlen, Action, Alen) ->
    yystate(55, Ics, Line, Tlen+1, Action, Alen);
yystate(69, [43|Ics], Line, Tlen, Action, Alen) ->
    yystate(59, Ics, Line, Tlen+1, Action, Alen);
yystate(69, [42|Ics], Line, Tlen, Action, Alen) ->
    yystate(46, Ics, Line, Tlen+1, Action, Alen);
yystate(69, [41|Ics], Line, Tlen, Action, Alen) ->
    yystate(63, Ics, Line, Tlen+1, Action, Alen);
yystate(69, [40|Ics], Line, Tlen, Action, Alen) ->
    yystate(54, Ics, Line, Tlen+1, Action, Alen);
yystate(69, [39|Ics], Line, Tlen, Action, Alen) ->
    yystate(8, Ics, Line, Tlen+1, Action, Alen);
yystate(69, [37|Ics], Line, Tlen, Action, Alen) ->
    yystate(71, Ics, Line, Tlen+1, Action, Alen);
yystate(69, [38|Ics], Line, Tlen, Action, Alen) ->
    yystate(71, Ics, Line, Tlen+1, Action, Alen);
yystate(69, [36|Ics], Line, Tlen, Action, Alen) ->
    yystate(0, Ics, Line, Tlen+1, Action, Alen);
yystate(69, [35|Ics], Line, Tlen, Action, Alen) ->
    yystate(7, Ics, Line, Tlen+1, Action, Alen);
yystate(69, [34|Ics], Line, Tlen, Action, Alen) ->
    yystate(27, Ics, Line, Tlen+1, Action, Alen);
yystate(69, [33|Ics], Line, Tlen, Action, Alen) ->
    yystate(71, Ics, Line, Tlen+1, Action, Alen);
yystate(69, [32|Ics], Line, Tlen, Action, Alen) ->
    yystate(39, Ics, Line, Tlen+1, Action, Alen);
yystate(69, [12|Ics], Line, Tlen, Action, Alen) ->
    yystate(67, Ics, Line, Tlen+1, Action, Alen);
yystate(69, [13|Ics], Line, Tlen, Action, Alen) ->
    yystate(67, Ics, Line, Tlen+1, Action, Alen);
yystate(69, [11|Ics], Line, Tlen, Action, Alen) ->
    yystate(71, Ics, Line, Tlen+1, Action, Alen);
yystate(69, [10|Ics], Line, Tlen, Action, Alen) ->
    yystate(67, Ics, Line+1, Tlen+1, Action, Alen);
yystate(69, [9|Ics], Line, Tlen, Action, Alen) ->
    yystate(67, Ics, Line, Tlen+1, Action, Alen);
yystate(69, [C|Ics], Line, Tlen, Action, Alen) when C >= 0, C =< 8 ->
    yystate(71, Ics, Line, Tlen+1, Action, Alen);
yystate(69, [C|Ics], Line, Tlen, Action, Alen) when C >= 14, C =< 31 ->
    yystate(71, Ics, Line, Tlen+1, Action, Alen);
yystate(69, [C|Ics], Line, Tlen, Action, Alen) when C >= 48, C =< 57 ->
    yystate(30, Ics, Line, Tlen+1, Action, Alen);
yystate(69, [C|Ics], Line, Tlen, Action, Alen) when C >= 65, C =< 90 ->
    yystate(30, Ics, Line, Tlen+1, Action, Alen);
yystate(69, [C|Ics], Line, Tlen, Action, Alen) when C >= 97, C =< 122 ->
    yystate(30, Ics, Line, Tlen+1, Action, Alen);
yystate(69, [C|Ics], Line, Tlen, Action, Alen) when C >= 127 ->
    yystate(71, Ics, Line, Tlen+1, Action, Alen);
yystate(69, Ics, Line, Tlen, Action, Alen) ->
    {Action,Alen,Tlen,Ics,Line,69};
yystate(68, Ics, Line, Tlen, _, _) ->
    {11,Tlen,Ics,Line};
yystate(67, [126|Ics], Line, Tlen, _, _) ->
    yystate(57, Ics, Line, Tlen+1, 25, Tlen);
yystate(67, [124|Ics], Line, Tlen, _, _) ->
    yystate(45, Ics, Line, Tlen+1, 25, Tlen);
yystate(67, [62|Ics], Line, Tlen, _, _) ->
    yystate(51, Ics, Line, Tlen+1, 25, Tlen);
yystate(67, [44|Ics], Line, Tlen, _, _) ->
    yystate(55, Ics, Line, Tlen+1, 25, Tlen);
yystate(67, [43|Ics], Line, Tlen, _, _) ->
    yystate(59, Ics, Line, Tlen+1, 25, Tlen);
yystate(67, [41|Ics], Line, Tlen, _, _) ->
    yystate(63, Ics, Line, Tlen+1, 25, Tlen);
yystate(67, [32|Ics], Line, Tlen, _, _) ->
    yystate(67, Ics, Line, Tlen+1, 25, Tlen);
yystate(67, [12|Ics], Line, Tlen, _, _) ->
    yystate(67, Ics, Line, Tlen+1, 25, Tlen);
yystate(67, [13|Ics], Line, Tlen, _, _) ->
    yystate(67, Ics, Line, Tlen+1, 25, Tlen);
yystate(67, [9|Ics], Line, Tlen, _, _) ->
    yystate(67, Ics, Line, Tlen+1, 25, Tlen);
yystate(67, [10|Ics], Line, Tlen, _, _) ->
    yystate(67, Ics, Line+1, Tlen+1, 25, Tlen);
yystate(67, Ics, Line, Tlen, _, _) ->
    {25,Tlen,Ics,Line,67};
yystate(66, Ics, Line, Tlen, _, _) ->
    {9,Tlen,Ics,Line};
yystate(65, [61|Ics], Line, Tlen, _, _) ->
    yystate(61, Ics, Line, Tlen+1, 23, Tlen);
yystate(65, [32|Ics], Line, Tlen, _, _) ->
    yystate(57, Ics, Line, Tlen+1, 23, Tlen);
yystate(65, [12|Ics], Line, Tlen, _, _) ->
    yystate(57, Ics, Line, Tlen+1, 23, Tlen);
yystate(65, [13|Ics], Line, Tlen, _, _) ->
    yystate(57, Ics, Line, Tlen+1, 23, Tlen);
yystate(65, [9|Ics], Line, Tlen, _, _) ->
    yystate(57, Ics, Line, Tlen+1, 23, Tlen);
yystate(65, [10|Ics], Line, Tlen, _, _) ->
    yystate(57, Ics, Line+1, Tlen+1, 23, Tlen);
yystate(65, Ics, Line, Tlen, _, _) ->
    {23,Tlen,Ics,Line,65};
yystate(64, Ics, Line, Tlen, _, _) ->
    {10,Tlen,Ics,Line};
yystate(63, Ics, Line, Tlen, _, _) ->
    {13,Tlen,Ics,Line};
yystate(62, [41|Ics], Line, Tlen, Action, Alen) ->
    yystate(66, Ics, Line, Tlen+1, Action, Alen);
yystate(62, Ics, Line, Tlen, Action, Alen) ->
    {Action,Alen,Tlen,Ics,Line,62};
yystate(61, Ics, Line, Tlen, _, _) ->
    {14,Tlen,Ics,Line};
yystate(60, [41|Ics], Line, Tlen, Action, Alen) ->
    yystate(64, Ics, Line, Tlen+1, Action, Alen);
yystate(60, Ics, Line, Tlen, Action, Alen) ->
    {Action,Alen,Tlen,Ics,Line,60};
yystate(59, [32|Ics], Line, Tlen, _, _) ->
    yystate(59, Ics, Line, Tlen+1, 22, Tlen);
yystate(59, [12|Ics], Line, Tlen, _, _) ->
    yystate(59, Ics, Line, Tlen+1, 22, Tlen);
yystate(59, [13|Ics], Line, Tlen, _, _) ->
    yystate(59, Ics, Line, Tlen+1, 22, Tlen);
yystate(59, [9|Ics], Line, Tlen, _, _) ->
    yystate(59, Ics, Line, Tlen+1, 22, Tlen);
yystate(59, [10|Ics], Line, Tlen, _, _) ->
    yystate(59, Ics, Line+1, Tlen+1, 22, Tlen);
yystate(59, Ics, Line, Tlen, _, _) ->
    {22,Tlen,Ics,Line,59};
yystate(58, [100|Ics], Line, Tlen, Action, Alen) ->
    yystate(62, Ics, Line, Tlen+1, Action, Alen);
yystate(58, [68|Ics], Line, Tlen, Action, Alen) ->
    yystate(62, Ics, Line, Tlen+1, Action, Alen);
yystate(58, Ics, Line, Tlen, Action, Alen) ->
    {Action,Alen,Tlen,Ics,Line,58};
yystate(57, [32|Ics], Line, Tlen, _, _) ->
    yystate(57, Ics, Line, Tlen+1, 23, Tlen);
yystate(57, [12|Ics], Line, Tlen, _, _) ->
    yystate(57, Ics, Line, Tlen+1, 23, Tlen);
yystate(57, [13|Ics], Line, Tlen, _, _) ->
    yystate(57, Ics, Line, Tlen+1, 23, Tlen);
yystate(57, [9|Ics], Line, Tlen, _, _) ->
    yystate(57, Ics, Line, Tlen+1, 23, Tlen);
yystate(57, [10|Ics], Line, Tlen, _, _) ->
    yystate(57, Ics, Line+1, Tlen+1, 23, Tlen);
yystate(57, Ics, Line, Tlen, _, _) ->
    {23,Tlen,Ics,Line,57};
yystate(56, [110|Ics], Line, Tlen, Action, Alen) ->
    yystate(60, Ics, Line, Tlen+1, Action, Alen);
yystate(56, [78|Ics], Line, Tlen, Action, Alen) ->
    yystate(60, Ics, Line, Tlen+1, Action, Alen);
yystate(56, Ics, Line, Tlen, Action, Alen) ->
    {Action,Alen,Tlen,Ics,Line,56};
yystate(55, [32|Ics], Line, Tlen, _, _) ->
    yystate(55, Ics, Line, Tlen+1, 20, Tlen);
yystate(55, [12|Ics], Line, Tlen, _, _) ->
    yystate(55, Ics, Line, Tlen+1, 20, Tlen);
yystate(55, [13|Ics], Line, Tlen, _, _) ->
    yystate(55, Ics, Line, Tlen+1, 20, Tlen);
yystate(55, [9|Ics], Line, Tlen, _, _) ->
    yystate(55, Ics, Line, Tlen+1, 20, Tlen);
yystate(55, [10|Ics], Line, Tlen, _, _) ->
    yystate(55, Ics, Line+1, Tlen+1, 20, Tlen);
yystate(55, Ics, Line, Tlen, _, _) ->
    {20,Tlen,Ics,Line,55};
yystate(54, [111|Ics], Line, Tlen, _, _) ->
    yystate(48, Ics, Line, Tlen+1, 26, Tlen);
yystate(54, [110|Ics], Line, Tlen, _, _) ->
    yystate(44, Ics, Line, Tlen+1, 26, Tlen);
yystate(54, [101|Ics], Line, Tlen, _, _) ->
    yystate(40, Ics, Line, Tlen+1, 26, Tlen);
yystate(54, [79|Ics], Line, Tlen, _, _) ->
    yystate(48, Ics, Line, Tlen+1, 26, Tlen);
yystate(54, [78|Ics], Line, Tlen, _, _) ->
    yystate(44, Ics, Line, Tlen+1, 26, Tlen);
yystate(54, [69|Ics], Line, Tlen, _, _) ->
    yystate(40, Ics, Line, Tlen+1, 26, Tlen);
yystate(54, [45|Ics], Line, Tlen, _, _) ->
    yystate(28, Ics, Line, Tlen+1, 26, Tlen);
yystate(54, [43|Ics], Line, Tlen, _, _) ->
    yystate(28, Ics, Line, Tlen+1, 26, Tlen);
yystate(54, [39|Ics], Line, Tlen, _, _) ->
    yystate(20, Ics, Line, Tlen+1, 26, Tlen);
yystate(54, [34|Ics], Line, Tlen, _, _) ->
    yystate(12, Ics, Line, Tlen+1, 26, Tlen);
yystate(54, [C|Ics], Line, Tlen, _, _) when C >= 48, C =< 57 ->
    yystate(36, Ics, Line, Tlen+1, 26, Tlen);
yystate(54, Ics, Line, Tlen, _, _) ->
    {26,Tlen,Ics,Line,54};
yystate(53, [61|Ics], Line, Tlen, _, _) ->
    yystate(49, Ics, Line, Tlen+1, 24, Tlen);
yystate(53, [32|Ics], Line, Tlen, _, _) ->
    yystate(45, Ics, Line, Tlen+1, 24, Tlen);
yystate(53, [12|Ics], Line, Tlen, _, _) ->
    yystate(45, Ics, Line, Tlen+1, 24, Tlen);
yystate(53, [13|Ics], Line, Tlen, _, _) ->
    yystate(45, Ics, Line, Tlen+1, 24, Tlen);
yystate(53, [9|Ics], Line, Tlen, _, _) ->
    yystate(45, Ics, Line, Tlen+1, 24, Tlen);
yystate(53, [10|Ics], Line, Tlen, _, _) ->
    yystate(45, Ics, Line+1, Tlen+1, 24, Tlen);
yystate(53, Ics, Line, Tlen, _, _) ->
    {24,Tlen,Ics,Line,53};
yystate(52, [101|Ics], Line, Tlen, Action, Alen) ->
    yystate(56, Ics, Line, Tlen+1, Action, Alen);
yystate(52, [69|Ics], Line, Tlen, Action, Alen) ->
    yystate(56, Ics, Line, Tlen+1, Action, Alen);
yystate(52, Ics, Line, Tlen, Action, Alen) ->
    {Action,Alen,Tlen,Ics,Line,52};
yystate(51, [32|Ics], Line, Tlen, _, _) ->
    yystate(51, Ics, Line, Tlen+1, 21, Tlen);
yystate(51, [12|Ics], Line, Tlen, _, _) ->
    yystate(51, Ics, Line, Tlen+1, 21, Tlen);
yystate(51, [13|Ics], Line, Tlen, _, _) ->
    yystate(51, Ics, Line, Tlen+1, 21, Tlen);
yystate(51, [9|Ics], Line, Tlen, _, _) ->
    yystate(51, Ics, Line, Tlen+1, 21, Tlen);
yystate(51, [10|Ics], Line, Tlen, _, _) ->
    yystate(51, Ics, Line+1, Tlen+1, 21, Tlen);
yystate(51, Ics, Line, Tlen, _, _) ->
    {21,Tlen,Ics,Line,51};
yystate(50, Ics, Line, Tlen, _, _) ->
    {18,Tlen,Ics,Line};
yystate(49, Ics, Line, Tlen, _, _) ->
    {15,Tlen,Ics,Line};
yystate(48, [100|Ics], Line, Tlen, Action, Alen) ->
    yystate(58, Ics, Line, Tlen+1, Action, Alen);
yystate(48, [68|Ics], Line, Tlen, Action, Alen) ->
    yystate(58, Ics, Line, Tlen+1, Action, Alen);
yystate(48, Ics, Line, Tlen, Action, Alen) ->
    {Action,Alen,Tlen,Ics,Line,48};
yystate(47, Ics, Line, Tlen, _, _) ->
    {2,Tlen,Ics,Line};
yystate(46, [61|Ics], Line, Tlen, _, _) ->
    yystate(50, Ics, Line, Tlen+1, 3, Tlen);
yystate(46, Ics, Line, Tlen, _, _) ->
    {3,Tlen,Ics,Line,46};
yystate(45, [32|Ics], Line, Tlen, _, _) ->
    yystate(45, Ics, Line, Tlen+1, 24, Tlen);
yystate(45, [12|Ics], Line, Tlen, _, _) ->
    yystate(45, Ics, Line, Tlen+1, 24, Tlen);
yystate(45, [13|Ics], Line, Tlen, _, _) ->
    yystate(45, Ics, Line, Tlen+1, 24, Tlen);
yystate(45, [9|Ics], Line, Tlen, _, _) ->
    yystate(45, Ics, Line, Tlen+1, 24, Tlen);
yystate(45, [10|Ics], Line, Tlen, _, _) ->
    yystate(45, Ics, Line+1, Tlen+1, 24, Tlen);
yystate(45, Ics, Line, Tlen, _, _) ->
    {24,Tlen,Ics,Line,45};
yystate(44, [45|Ics], Line, Tlen, Action, Alen) ->
    yystate(70, Ics, Line, Tlen+1, Action, Alen);
yystate(44, [43|Ics], Line, Tlen, Action, Alen) ->
    yystate(70, Ics, Line, Tlen+1, Action, Alen);
yystate(44, [41|Ics], Line, Tlen, Action, Alen) ->
    yystate(68, Ics, Line, Tlen+1, Action, Alen);
yystate(44, Ics, Line, Tlen, Action, Alen) ->
    {Action,Alen,Tlen,Ics,Line,44};
yystate(43, [93|Ics], Line, Tlen, Action, Alen) ->
    yystate(47, Ics, Line, Tlen+1, Action, Alen);
yystate(43, Ics, Line, Tlen, Action, Alen) ->
    {Action,Alen,Tlen,Ics,Line,43};
yystate(42, [95|Ics], Line, Tlen, _, _) ->
    yystate(42, Ics, Line, Tlen+1, 0, Tlen);
yystate(42, [92|Ics], Line, Tlen, _, _) ->
    yystate(34, Ics, Line, Tlen+1, 0, Tlen);
yystate(42, [45|Ics], Line, Tlen, _, _) ->
    yystate(42, Ics, Line, Tlen+1, 0, Tlen);
yystate(42, [C|Ics], Line, Tlen, _, _) when C >= 48, C =< 57 ->
    yystate(42, Ics, Line, Tlen+1, 0, Tlen);
yystate(42, [C|Ics], Line, Tlen, _, _) when C >= 65, C =< 90 ->
    yystate(42, Ics, Line, Tlen+1, 0, Tlen);
yystate(42, [C|Ics], Line, Tlen, _, _) when C >= 97, C =< 122 ->
    yystate(42, Ics, Line, Tlen+1, 0, Tlen);
yystate(42, Ics, Line, Tlen, _, _) ->
    {0,Tlen,Ics,Line,42};
yystate(41, [61|Ics], Line, Tlen, _, _) ->
    yystate(37, Ics, Line, Tlen+1, 26, Tlen);
yystate(41, Ics, Line, Tlen, _, _) ->
    {26,Tlen,Ics,Line,41};
yystate(40, [118|Ics], Line, Tlen, Action, Alen) ->
    yystate(52, Ics, Line, Tlen+1, Action, Alen);
yystate(40, [86|Ics], Line, Tlen, Action, Alen) ->
    yystate(52, Ics, Line, Tlen+1, Action, Alen);
yystate(40, Ics, Line, Tlen, Action, Alen) ->
    {Action,Alen,Tlen,Ics,Line,40};
yystate(39, [126|Ics], Line, Tlen, _, _) ->
    yystate(57, Ics, Line, Tlen+1, 25, Tlen);
yystate(39, [124|Ics], Line, Tlen, _, _) ->
    yystate(45, Ics, Line, Tlen+1, 25, Tlen);
yystate(39, [115|Ics], Line, Tlen, _, _) ->
    yystate(43, Ics, Line, Tlen+1, 25, Tlen);
yystate(39, [105|Ics], Line, Tlen, _, _) ->
    yystate(43, Ics, Line, Tlen+1, 25, Tlen);
yystate(39, [62|Ics], Line, Tlen, _, _) ->
    yystate(51, Ics, Line, Tlen+1, 25, Tlen);
yystate(39, [44|Ics], Line, Tlen, _, _) ->
    yystate(55, Ics, Line, Tlen+1, 25, Tlen);
yystate(39, [43|Ics], Line, Tlen, _, _) ->
    yystate(59, Ics, Line, Tlen+1, 25, Tlen);
yystate(39, [41|Ics], Line, Tlen, _, _) ->
    yystate(63, Ics, Line, Tlen+1, 25, Tlen);
yystate(39, [32|Ics], Line, Tlen, _, _) ->
    yystate(67, Ics, Line, Tlen+1, 25, Tlen);
yystate(39, [12|Ics], Line, Tlen, _, _) ->
    yystate(67, Ics, Line, Tlen+1, 25, Tlen);
yystate(39, [13|Ics], Line, Tlen, _, _) ->
    yystate(67, Ics, Line, Tlen+1, 25, Tlen);
yystate(39, [9|Ics], Line, Tlen, _, _) ->
    yystate(67, Ics, Line, Tlen+1, 25, Tlen);
yystate(39, [10|Ics], Line, Tlen, _, _) ->
    yystate(67, Ics, Line+1, Tlen+1, 25, Tlen);
yystate(39, Ics, Line, Tlen, _, _) ->
    {25,Tlen,Ics,Line,39};
yystate(38, [95|Ics], Line, Tlen, Action, Alen) ->
    yystate(42, Ics, Line, Tlen+1, Action, Alen);
yystate(38, [45|Ics], Line, Tlen, Action, Alen) ->
    yystate(42, Ics, Line, Tlen+1, Action, Alen);
yystate(38, [C|Ics], Line, Tlen, Action, Alen) when C >= 48, C =< 57 ->
    yystate(42, Ics, Line, Tlen+1, Action, Alen);
yystate(38, [C|Ics], Line, Tlen, Action, Alen) when C >= 65, C =< 90 ->
    yystate(42, Ics, Line, Tlen+1, Action, Alen);
yystate(38, [C|Ics], Line, Tlen, Action, Alen) when C >= 97, C =< 122 ->
    yystate(42, Ics, Line, Tlen+1, Action, Alen);
yystate(38, Ics, Line, Tlen, Action, Alen) ->
    {Action,Alen,Tlen,Ics,Line,38};
yystate(37, Ics, Line, Tlen, _, _) ->
    {16,Tlen,Ics,Line};
yystate(36, [110|Ics], Line, Tlen, Action, Alen) ->
    yystate(44, Ics, Line, Tlen+1, Action, Alen);
yystate(36, [78|Ics], Line, Tlen, Action, Alen) ->
    yystate(44, Ics, Line, Tlen+1, Action, Alen);
yystate(36, [41|Ics], Line, Tlen, Action, Alen) ->
    yystate(32, Ics, Line, Tlen+1, Action, Alen);
yystate(36, [C|Ics], Line, Tlen, Action, Alen) when C >= 48, C =< 57 ->
    yystate(36, Ics, Line, Tlen+1, Action, Alen);
yystate(36, Ics, Line, Tlen, Action, Alen) ->
    {Action,Alen,Tlen,Ics,Line,36};
yystate(35, [34|Ics], Line, Tlen, Action, Alen) ->
    yystate(31, Ics, Line, Tlen+1, Action, Alen);
yystate(35, [10|Ics], Line, Tlen, Action, Alen) ->
    yystate(35, Ics, Line+1, Tlen+1, Action, Alen);
yystate(35, [C|Ics], Line, Tlen, Action, Alen) when C >= 0, C =< 9 ->
    yystate(35, Ics, Line, Tlen+1, Action, Alen);
yystate(35, [C|Ics], Line, Tlen, Action, Alen) when C >= 11, C =< 33 ->
    yystate(35, Ics, Line, Tlen+1, Action, Alen);
yystate(35, [C|Ics], Line, Tlen, Action, Alen) when C >= 35 ->
    yystate(35, Ics, Line, Tlen+1, Action, Alen);
yystate(35, Ics, Line, Tlen, Action, Alen) ->
    {Action,Alen,Tlen,Ics,Line,35};
yystate(34, [58|Ics], Line, Tlen, Action, Alen) ->
    yystate(38, Ics, Line, Tlen+1, Action, Alen);
yystate(34, [46|Ics], Line, Tlen, Action, Alen) ->
    yystate(38, Ics, Line, Tlen+1, Action, Alen);
yystate(34, Ics, Line, Tlen, Action, Alen) ->
    {Action,Alen,Tlen,Ics,Line,34};
yystate(33, Ics, Line, Tlen, _, _) ->
    {3,Tlen,Ics,Line};
yystate(32, Ics, Line, Tlen, _, _) ->
    {8,Tlen,Ics,Line};
yystate(31, Ics, Line, Tlen, _, _) ->
    {1,Tlen,Ics,Line};
yystate(30, [95|Ics], Line, Tlen, _, _) ->
    yystate(30, Ics, Line, Tlen+1, 0, Tlen);
yystate(30, [92|Ics], Line, Tlen, _, _) ->
    yystate(34, Ics, Line, Tlen+1, 0, Tlen);
yystate(30, [45|Ics], Line, Tlen, _, _) ->
    yystate(30, Ics, Line, Tlen+1, 0, Tlen);
yystate(30, [C|Ics], Line, Tlen, _, _) when C >= 48, C =< 57 ->
    yystate(30, Ics, Line, Tlen+1, 0, Tlen);
yystate(30, [C|Ics], Line, Tlen, _, _) when C >= 65, C =< 90 ->
    yystate(30, Ics, Line, Tlen+1, 0, Tlen);
yystate(30, [C|Ics], Line, Tlen, _, _) when C >= 97, C =< 122 ->
    yystate(30, Ics, Line, Tlen+1, 0, Tlen);
yystate(30, Ics, Line, Tlen, _, _) ->
    {0,Tlen,Ics,Line,30};
yystate(29, Ics, Line, Tlen, _, _) ->
    {19,Tlen,Ics,Line};
yystate(28, [110|Ics], Line, Tlen, Action, Alen) ->
    yystate(44, Ics, Line, Tlen+1, Action, Alen);
yystate(28, [78|Ics], Line, Tlen, Action, Alen) ->
    yystate(44, Ics, Line, Tlen+1, Action, Alen);
yystate(28, [C|Ics], Line, Tlen, Action, Alen) when C >= 48, C =< 57 ->
    yystate(28, Ics, Line, Tlen+1, Action, Alen);
yystate(28, Ics, Line, Tlen, Action, Alen) ->
    {Action,Alen,Tlen,Ics,Line,28};
yystate(27, [34|Ics], Line, Tlen, _, _) ->
    yystate(31, Ics, Line, Tlen+1, 26, Tlen);
yystate(27, [10|Ics], Line, Tlen, _, _) ->
    yystate(35, Ics, Line+1, Tlen+1, 26, Tlen);
yystate(27, [C|Ics], Line, Tlen, _, _) when C >= 0, C =< 9 ->
    yystate(35, Ics, Line, Tlen+1, 26, Tlen);
yystate(27, [C|Ics], Line, Tlen, _, _) when C >= 11, C =< 33 ->
    yystate(35, Ics, Line, Tlen+1, 26, Tlen);
yystate(27, [C|Ics], Line, Tlen, _, _) when C >= 35 ->
    yystate(35, Ics, Line, Tlen+1, 26, Tlen);
yystate(27, Ics, Line, Tlen, _, _) ->
    {26,Tlen,Ics,Line,27};
yystate(26, [95|Ics], Line, Tlen, _, _) ->
    yystate(26, Ics, Line, Tlen+1, 5, Tlen);
yystate(26, [92|Ics], Line, Tlen, _, _) ->
    yystate(18, Ics, Line, Tlen+1, 5, Tlen);
yystate(26, [45|Ics], Line, Tlen, _, _) ->
    yystate(26, Ics, Line, Tlen+1, 5, Tlen);
yystate(26, [C|Ics], Line, Tlen, _, _) when C >= 48, C =< 57 ->
    yystate(26, Ics, Line, Tlen+1, 5, Tlen);
yystate(26, [C|Ics], Line, Tlen, _, _) when C >= 65, C =< 90 ->
    yystate(26, Ics, Line, Tlen+1, 5, Tlen);
yystate(26, [C|Ics], Line, Tlen, _, _) when C >= 97, C =< 122 ->
    yystate(26, Ics, Line, Tlen+1, 5, Tlen);
yystate(26, Ics, Line, Tlen, _, _) ->
    {5,Tlen,Ics,Line,26};
yystate(25, [110|Ics], Line, Tlen, _, _) ->
    yystate(21, Ics, Line, Tlen+1, 26, Tlen);
yystate(25, [95|Ics], Line, Tlen, _, _) ->
    yystate(6, Ics, Line, Tlen+1, 26, Tlen);
yystate(25, [78|Ics], Line, Tlen, _, _) ->
    yystate(21, Ics, Line, Tlen+1, 26, Tlen);
yystate(25, [45|Ics], Line, Tlen, _, _) ->
    yystate(6, Ics, Line, Tlen+1, 26, Tlen);
yystate(25, [C|Ics], Line, Tlen, _, _) when C >= 48, C =< 57 ->
    yystate(6, Ics, Line, Tlen+1, 26, Tlen);
yystate(25, [C|Ics], Line, Tlen, _, _) when C >= 65, C =< 77 ->
    yystate(6, Ics, Line, Tlen+1, 26, Tlen);
yystate(25, [C|Ics], Line, Tlen, _, _) when C >= 79, C =< 90 ->
    yystate(6, Ics, Line, Tlen+1, 26, Tlen);
yystate(25, [C|Ics], Line, Tlen, _, _) when C >= 97, C =< 109 ->
    yystate(6, Ics, Line, Tlen+1, 26, Tlen);
yystate(25, [C|Ics], Line, Tlen, _, _) when C >= 111, C =< 122 ->
    yystate(6, Ics, Line, Tlen+1, 26, Tlen);
yystate(25, Ics, Line, Tlen, _, _) ->
    {26,Tlen,Ics,Line,25};
yystate(24, Ics, Line, Tlen, _, _) ->
    {12,Tlen,Ics,Line};
yystate(23, [95|Ics], Line, Tlen, _, _) ->
    yystate(23, Ics, Line, Tlen+1, 4, Tlen);
yystate(23, [92|Ics], Line, Tlen, _, _) ->
    yystate(11, Ics, Line, Tlen+1, 4, Tlen);
yystate(23, [45|Ics], Line, Tlen, _, _) ->
    yystate(23, Ics, Line, Tlen+1, 4, Tlen);
yystate(23, [C|Ics], Line, Tlen, _, _) when C >= 48, C =< 57 ->
    yystate(23, Ics, Line, Tlen+1, 4, Tlen);
yystate(23, [C|Ics], Line, Tlen, _, _) when C >= 65, C =< 90 ->
    yystate(23, Ics, Line, Tlen+1, 4, Tlen);
yystate(23, [C|Ics], Line, Tlen, _, _) when C >= 97, C =< 122 ->
    yystate(23, Ics, Line, Tlen+1, 4, Tlen);
yystate(23, Ics, Line, Tlen, _, _) ->
    {4,Tlen,Ics,Line,23};
yystate(22, [95|Ics], Line, Tlen, Action, Alen) ->
    yystate(26, Ics, Line, Tlen+1, Action, Alen);
yystate(22, [45|Ics], Line, Tlen, Action, Alen) ->
    yystate(26, Ics, Line, Tlen+1, Action, Alen);
yystate(22, [C|Ics], Line, Tlen, Action, Alen) when C >= 48, C =< 57 ->
    yystate(26, Ics, Line, Tlen+1, Action, Alen);
yystate(22, [C|Ics], Line, Tlen, Action, Alen) when C >= 65, C =< 90 ->
    yystate(26, Ics, Line, Tlen+1, Action, Alen);
yystate(22, [C|Ics], Line, Tlen, Action, Alen) when C >= 97, C =< 122 ->
    yystate(26, Ics, Line, Tlen+1, Action, Alen);
yystate(22, Ics, Line, Tlen, Action, Alen) ->
    {Action,Alen,Tlen,Ics,Line,22};
yystate(21, [111|Ics], Line, Tlen, _, _) ->
    yystate(17, Ics, Line, Tlen+1, 7, Tlen);
yystate(21, [95|Ics], Line, Tlen, _, _) ->
    yystate(6, Ics, Line, Tlen+1, 7, Tlen);
yystate(21, [92|Ics], Line, Tlen, _, _) ->
    yystate(5, Ics, Line, Tlen+1, 7, Tlen);
yystate(21, [79|Ics], Line, Tlen, _, _) ->
    yystate(17, Ics, Line, Tlen+1, 7, Tlen);
yystate(21, [45|Ics], Line, Tlen, _, _) ->
    yystate(6, Ics, Line, Tlen+1, 7, Tlen);
yystate(21, [C|Ics], Line, Tlen, _, _) when C >= 48, C =< 57 ->
    yystate(6, Ics, Line, Tlen+1, 7, Tlen);
yystate(21, [C|Ics], Line, Tlen, _, _) when C >= 65, C =< 78 ->
    yystate(6, Ics, Line, Tlen+1, 7, Tlen);
yystate(21, [C|Ics], Line, Tlen, _, _) when C >= 80, C =< 90 ->
    yystate(6, Ics, Line, Tlen+1, 7, Tlen);
yystate(21, [C|Ics], Line, Tlen, _, _) when C >= 97, C =< 110 ->
    yystate(6, Ics, Line, Tlen+1, 7, Tlen);
yystate(21, [C|Ics], Line, Tlen, _, _) when C >= 112, C =< 122 ->
    yystate(6, Ics, Line, Tlen+1, 7, Tlen);
yystate(21, Ics, Line, Tlen, _, _) ->
    {7,Tlen,Ics,Line,21};
yystate(20, [39|Ics], Line, Tlen, Action, Alen) ->
    yystate(16, Ics, Line, Tlen+1, Action, Alen);
yystate(20, [10|Ics], Line, Tlen, Action, Alen) ->
    yystate(20, Ics, Line+1, Tlen+1, Action, Alen);
yystate(20, [C|Ics], Line, Tlen, Action, Alen) when C >= 0, C =< 9 ->
    yystate(20, Ics, Line, Tlen+1, Action, Alen);
yystate(20, [C|Ics], Line, Tlen, Action, Alen) when C >= 11, C =< 38 ->
    yystate(20, Ics, Line, Tlen+1, Action, Alen);
yystate(20, [C|Ics], Line, Tlen, Action, Alen) when C >= 40 ->
    yystate(20, Ics, Line, Tlen+1, Action, Alen);
yystate(20, Ics, Line, Tlen, Action, Alen) ->
    {Action,Alen,Tlen,Ics,Line,20};
yystate(19, [95|Ics], Line, Tlen, _, _) ->
    yystate(19, Ics, Line, Tlen+1, 4, Tlen);
yystate(19, [92|Ics], Line, Tlen, _, _) ->
    yystate(11, Ics, Line, Tlen+1, 4, Tlen);
yystate(19, [45|Ics], Line, Tlen, _, _) ->
    yystate(19, Ics, Line, Tlen+1, 4, Tlen);
yystate(19, [C|Ics], Line, Tlen, _, _) when C >= 48, C =< 57 ->
    yystate(19, Ics, Line, Tlen+1, 4, Tlen);
yystate(19, [C|Ics], Line, Tlen, _, _) when C >= 65, C =< 90 ->
    yystate(19, Ics, Line, Tlen+1, 4, Tlen);
yystate(19, [C|Ics], Line, Tlen, _, _) when C >= 97, C =< 122 ->
    yystate(19, Ics, Line, Tlen+1, 4, Tlen);
yystate(19, Ics, Line, Tlen, _, _) ->
    {4,Tlen,Ics,Line,19};
yystate(18, [58|Ics], Line, Tlen, Action, Alen) ->
    yystate(22, Ics, Line, Tlen+1, Action, Alen);
yystate(18, [46|Ics], Line, Tlen, Action, Alen) ->
    yystate(22, Ics, Line, Tlen+1, Action, Alen);
yystate(18, Ics, Line, Tlen, Action, Alen) ->
    {Action,Alen,Tlen,Ics,Line,18};
yystate(17, [116|Ics], Line, Tlen, _, _) ->
    yystate(13, Ics, Line, Tlen+1, 7, Tlen);
yystate(17, [95|Ics], Line, Tlen, _, _) ->
    yystate(6, Ics, Line, Tlen+1, 7, Tlen);
yystate(17, [92|Ics], Line, Tlen, _, _) ->
    yystate(5, Ics, Line, Tlen+1, 7, Tlen);
yystate(17, [84|Ics], Line, Tlen, _, _) ->
    yystate(13, Ics, Line, Tlen+1, 7, Tlen);
yystate(17, [45|Ics], Line, Tlen, _, _) ->
    yystate(6, Ics, Line, Tlen+1, 7, Tlen);
yystate(17, [C|Ics], Line, Tlen, _, _) when C >= 48, C =< 57 ->
    yystate(6, Ics, Line, Tlen+1, 7, Tlen);
yystate(17, [C|Ics], Line, Tlen, _, _) when C >= 65, C =< 83 ->
    yystate(6, Ics, Line, Tlen+1, 7, Tlen);
yystate(17, [C|Ics], Line, Tlen, _, _) when C >= 85, C =< 90 ->
    yystate(6, Ics, Line, Tlen+1, 7, Tlen);
yystate(17, [C|Ics], Line, Tlen, _, _) when C >= 97, C =< 115 ->
    yystate(6, Ics, Line, Tlen+1, 7, Tlen);
yystate(17, [C|Ics], Line, Tlen, _, _) when C >= 117, C =< 122 ->
    yystate(6, Ics, Line, Tlen+1, 7, Tlen);
yystate(17, Ics, Line, Tlen, _, _) ->
    {7,Tlen,Ics,Line,17};
yystate(16, [41|Ics], Line, Tlen, Action, Alen) ->
    yystate(24, Ics, Line, Tlen+1, Action, Alen);
yystate(16, Ics, Line, Tlen, Action, Alen) ->
    {Action,Alen,Tlen,Ics,Line,16};
yystate(15, [95|Ics], Line, Tlen, Action, Alen) ->
    yystate(19, Ics, Line, Tlen+1, Action, Alen);
yystate(15, [45|Ics], Line, Tlen, Action, Alen) ->
    yystate(19, Ics, Line, Tlen+1, Action, Alen);
yystate(15, [C|Ics], Line, Tlen, Action, Alen) when C >= 48, C =< 57 ->
    yystate(19, Ics, Line, Tlen+1, Action, Alen);
yystate(15, [C|Ics], Line, Tlen, Action, Alen) when C >= 65, C =< 90 ->
    yystate(19, Ics, Line, Tlen+1, Action, Alen);
yystate(15, [C|Ics], Line, Tlen, Action, Alen) when C >= 97, C =< 122 ->
    yystate(19, Ics, Line, Tlen+1, Action, Alen);
yystate(15, Ics, Line, Tlen, Action, Alen) ->
    {Action,Alen,Tlen,Ics,Line,15};
yystate(14, [95|Ics], Line, Tlen, _, _) ->
    yystate(14, Ics, Line, Tlen+1, 5, Tlen);
yystate(14, [92|Ics], Line, Tlen, _, _) ->
    yystate(18, Ics, Line, Tlen+1, 5, Tlen);
yystate(14, [45|Ics], Line, Tlen, _, _) ->
    yystate(14, Ics, Line, Tlen+1, 5, Tlen);
yystate(14, [C|Ics], Line, Tlen, _, _) when C >= 48, C =< 57 ->
    yystate(14, Ics, Line, Tlen+1, 5, Tlen);
yystate(14, [C|Ics], Line, Tlen, _, _) when C >= 65, C =< 90 ->
    yystate(14, Ics, Line, Tlen+1, 5, Tlen);
yystate(14, [C|Ics], Line, Tlen, _, _) when C >= 97, C =< 122 ->
    yystate(14, Ics, Line, Tlen+1, 5, Tlen);
yystate(14, Ics, Line, Tlen, _, _) ->
    {5,Tlen,Ics,Line,14};
yystate(13, [95|Ics], Line, Tlen, _, _) ->
    yystate(6, Ics, Line, Tlen+1, 7, Tlen);
yystate(13, [92|Ics], Line, Tlen, _, _) ->
    yystate(5, Ics, Line, Tlen+1, 7, Tlen);
yystate(13, [45|Ics], Line, Tlen, _, _) ->
    yystate(6, Ics, Line, Tlen+1, 7, Tlen);
yystate(13, [40|Ics], Line, Tlen, _, _) ->
    yystate(9, Ics, Line, Tlen+1, 7, Tlen);
yystate(13, [C|Ics], Line, Tlen, _, _) when C >= 48, C =< 57 ->
    yystate(6, Ics, Line, Tlen+1, 7, Tlen);
yystate(13, [C|Ics], Line, Tlen, _, _) when C >= 65, C =< 90 ->
    yystate(6, Ics, Line, Tlen+1, 7, Tlen);
yystate(13, [C|Ics], Line, Tlen, _, _) when C >= 97, C =< 122 ->
    yystate(6, Ics, Line, Tlen+1, 7, Tlen);
yystate(13, Ics, Line, Tlen, _, _) ->
    {7,Tlen,Ics,Line,13};
yystate(12, [34|Ics], Line, Tlen, Action, Alen) ->
    yystate(16, Ics, Line, Tlen+1, Action, Alen);
yystate(12, [10|Ics], Line, Tlen, Action, Alen) ->
    yystate(12, Ics, Line+1, Tlen+1, Action, Alen);
yystate(12, [C|Ics], Line, Tlen, Action, Alen) when C >= 0, C =< 9 ->
    yystate(12, Ics, Line, Tlen+1, Action, Alen);
yystate(12, [C|Ics], Line, Tlen, Action, Alen) when C >= 11, C =< 33 ->
    yystate(12, Ics, Line, Tlen+1, Action, Alen);
yystate(12, [C|Ics], Line, Tlen, Action, Alen) when C >= 35 ->
    yystate(12, Ics, Line, Tlen+1, Action, Alen);
yystate(12, Ics, Line, Tlen, Action, Alen) ->
    {Action,Alen,Tlen,Ics,Line,12};
yystate(11, [58|Ics], Line, Tlen, Action, Alen) ->
    yystate(15, Ics, Line, Tlen+1, Action, Alen);
yystate(11, [46|Ics], Line, Tlen, Action, Alen) ->
    yystate(15, Ics, Line, Tlen+1, Action, Alen);
yystate(11, Ics, Line, Tlen, Action, Alen) ->
    {Action,Alen,Tlen,Ics,Line,11};
yystate(10, [95|Ics], Line, Tlen, _, _) ->
    yystate(14, Ics, Line, Tlen+1, 26, Tlen);
yystate(10, [45|Ics], Line, Tlen, _, _) ->
    yystate(14, Ics, Line, Tlen+1, 26, Tlen);
yystate(10, [C|Ics], Line, Tlen, _, _) when C >= 48, C =< 57 ->
    yystate(14, Ics, Line, Tlen+1, 26, Tlen);
yystate(10, [C|Ics], Line, Tlen, _, _) when C >= 65, C =< 90 ->
    yystate(14, Ics, Line, Tlen+1, 26, Tlen);
yystate(10, [C|Ics], Line, Tlen, _, _) when C >= 97, C =< 122 ->
    yystate(14, Ics, Line, Tlen+1, 26, Tlen);
yystate(10, Ics, Line, Tlen, _, _) ->
    {26,Tlen,Ics,Line,10};
yystate(9, Ics, Line, Tlen, _, _) ->
    {6,Tlen,Ics,Line};
yystate(8, [39|Ics], Line, Tlen, _, _) ->
    yystate(31, Ics, Line, Tlen+1, 26, Tlen);
yystate(8, [10|Ics], Line, Tlen, _, _) ->
    yystate(4, Ics, Line+1, Tlen+1, 26, Tlen);
yystate(8, [C|Ics], Line, Tlen, _, _) when C >= 0, C =< 9 ->
    yystate(4, Ics, Line, Tlen+1, 26, Tlen);
yystate(8, [C|Ics], Line, Tlen, _, _) when C >= 11, C =< 38 ->
    yystate(4, Ics, Line, Tlen+1, 26, Tlen);
yystate(8, [C|Ics], Line, Tlen, _, _) when C >= 40 ->
    yystate(4, Ics, Line, Tlen+1, 26, Tlen);
yystate(8, Ics, Line, Tlen, _, _) ->
    {26,Tlen,Ics,Line,8};
yystate(7, [95|Ics], Line, Tlen, _, _) ->
    yystate(23, Ics, Line, Tlen+1, 26, Tlen);
yystate(7, [45|Ics], Line, Tlen, _, _) ->
    yystate(23, Ics, Line, Tlen+1, 26, Tlen);
yystate(7, [C|Ics], Line, Tlen, _, _) when C >= 48, C =< 57 ->
    yystate(23, Ics, Line, Tlen+1, 26, Tlen);
yystate(7, [C|Ics], Line, Tlen, _, _) when C >= 65, C =< 90 ->
    yystate(23, Ics, Line, Tlen+1, 26, Tlen);
yystate(7, [C|Ics], Line, Tlen, _, _) when C >= 97, C =< 122 ->
    yystate(23, Ics, Line, Tlen+1, 26, Tlen);
yystate(7, Ics, Line, Tlen, _, _) ->
    {26,Tlen,Ics,Line,7};
yystate(6, [95|Ics], Line, Tlen, _, _) ->
    yystate(6, Ics, Line, Tlen+1, 7, Tlen);
yystate(6, [92|Ics], Line, Tlen, _, _) ->
    yystate(5, Ics, Line, Tlen+1, 7, Tlen);
yystate(6, [45|Ics], Line, Tlen, _, _) ->
    yystate(6, Ics, Line, Tlen+1, 7, Tlen);
yystate(6, [C|Ics], Line, Tlen, _, _) when C >= 48, C =< 57 ->
    yystate(6, Ics, Line, Tlen+1, 7, Tlen);
yystate(6, [C|Ics], Line, Tlen, _, _) when C >= 65, C =< 90 ->
    yystate(6, Ics, Line, Tlen+1, 7, Tlen);
yystate(6, [C|Ics], Line, Tlen, _, _) when C >= 97, C =< 122 ->
    yystate(6, Ics, Line, Tlen+1, 7, Tlen);
yystate(6, Ics, Line, Tlen, _, _) ->
    {7,Tlen,Ics,Line,6};
yystate(5, [58|Ics], Line, Tlen, Action, Alen) ->
    yystate(1, Ics, Line, Tlen+1, Action, Alen);
yystate(5, [46|Ics], Line, Tlen, Action, Alen) ->
    yystate(1, Ics, Line, Tlen+1, Action, Alen);
yystate(5, Ics, Line, Tlen, Action, Alen) ->
    {Action,Alen,Tlen,Ics,Line,5};
yystate(4, [39|Ics], Line, Tlen, Action, Alen) ->
    yystate(31, Ics, Line, Tlen+1, Action, Alen);
yystate(4, [10|Ics], Line, Tlen, Action, Alen) ->
    yystate(4, Ics, Line+1, Tlen+1, Action, Alen);
yystate(4, [C|Ics], Line, Tlen, Action, Alen) when C >= 0, C =< 9 ->
    yystate(4, Ics, Line, Tlen+1, Action, Alen);
yystate(4, [C|Ics], Line, Tlen, Action, Alen) when C >= 11, C =< 38 ->
    yystate(4, Ics, Line, Tlen+1, Action, Alen);
yystate(4, [C|Ics], Line, Tlen, Action, Alen) when C >= 40 ->
    yystate(4, Ics, Line, Tlen+1, Action, Alen);
yystate(4, Ics, Line, Tlen, Action, Alen) ->
    {Action,Alen,Tlen,Ics,Line,4};
yystate(3, Ics, Line, Tlen, _, _) ->
    {17,Tlen,Ics,Line};
yystate(2, [95|Ics], Line, Tlen, _, _) ->
    yystate(2, Ics, Line, Tlen+1, 7, Tlen);
yystate(2, [92|Ics], Line, Tlen, _, _) ->
    yystate(5, Ics, Line, Tlen+1, 7, Tlen);
yystate(2, [45|Ics], Line, Tlen, _, _) ->
    yystate(2, Ics, Line, Tlen+1, 7, Tlen);
yystate(2, [C|Ics], Line, Tlen, _, _) when C >= 48, C =< 57 ->
    yystate(2, Ics, Line, Tlen+1, 7, Tlen);
yystate(2, [C|Ics], Line, Tlen, _, _) when C >= 65, C =< 90 ->
    yystate(2, Ics, Line, Tlen+1, 7, Tlen);
yystate(2, [C|Ics], Line, Tlen, _, _) when C >= 97, C =< 122 ->
    yystate(2, Ics, Line, Tlen+1, 7, Tlen);
yystate(2, Ics, Line, Tlen, _, _) ->
    {7,Tlen,Ics,Line,2};
yystate(1, [95|Ics], Line, Tlen, Action, Alen) ->
    yystate(2, Ics, Line, Tlen+1, Action, Alen);
yystate(1, [45|Ics], Line, Tlen, Action, Alen) ->
    yystate(2, Ics, Line, Tlen+1, Action, Alen);
yystate(1, [C|Ics], Line, Tlen, Action, Alen) when C >= 48, C =< 57 ->
    yystate(2, Ics, Line, Tlen+1, Action, Alen);
yystate(1, [C|Ics], Line, Tlen, Action, Alen) when C >= 65, C =< 90 ->
    yystate(2, Ics, Line, Tlen+1, Action, Alen);
yystate(1, [C|Ics], Line, Tlen, Action, Alen) when C >= 97, C =< 122 ->
    yystate(2, Ics, Line, Tlen+1, Action, Alen);
yystate(1, Ics, Line, Tlen, Action, Alen) ->
    {Action,Alen,Tlen,Ics,Line,1};
yystate(0, [61|Ics], Line, Tlen, _, _) ->
    yystate(3, Ics, Line, Tlen+1, 26, Tlen);
yystate(0, Ics, Line, Tlen, _, _) ->
    {26,Tlen,Ics,Line,0};
yystate(S, Ics, Line, Tlen, Action, Alen) ->
    {Action,Alen,Tlen,Ics,Line,S}.

%% yyaction(Action, TokenLength, TokenChars, TokenLine) ->
%% {token,Token} | {end_token, Token} | skip_token | {error,String}.
%% Generated action function.

yyaction(0, TokenLen, YYtcs, TokenLine) ->
    TokenChars = yypre(YYtcs, TokenLen),
    yyaction_0(TokenChars, TokenLine);
yyaction(1, TokenLen, YYtcs, TokenLine) ->
    TokenChars = yypre(YYtcs, TokenLen),
    yyaction_1(TokenChars, TokenLine);
yyaction(2, TokenLen, YYtcs, TokenLine) ->
    TokenChars = yypre(YYtcs, TokenLen),
    yyaction_2(TokenChars, TokenLine);
yyaction(3, TokenLen, YYtcs, TokenLine) ->
    TokenChars = yypre(YYtcs, TokenLen),
    yyaction_3(TokenChars, TokenLine);
yyaction(4, TokenLen, YYtcs, TokenLine) ->
    TokenChars = yypre(YYtcs, TokenLen),
    yyaction_4(TokenChars, TokenLine);
yyaction(5, TokenLen, YYtcs, TokenLine) ->
    TokenChars = yypre(YYtcs, TokenLen),
    yyaction_5(TokenChars, TokenLine);
yyaction(6, _, _, TokenLine) ->
    yyaction_6(TokenLine);
yyaction(7, TokenLen, YYtcs, TokenLine) ->
    TokenChars = yypre(YYtcs, TokenLen),
    yyaction_7(TokenChars, TokenLine);
yyaction(8, TokenLen, YYtcs, TokenLine) ->
    TokenChars = yypre(YYtcs, TokenLen),
    yyaction_8(TokenChars, TokenLine);
yyaction(9, _, _, TokenLine) ->
    yyaction_9(TokenLine);
yyaction(10, _, _, TokenLine) ->
    yyaction_10(TokenLine);
yyaction(11, TokenLen, YYtcs, TokenLine) ->
    TokenChars = yypre(YYtcs, TokenLen),
    yyaction_11(TokenChars, TokenLine);
yyaction(12, TokenLen, YYtcs, TokenLine) ->
    TokenChars = yypre(YYtcs, TokenLen),
    yyaction_12(TokenChars, TokenLine);
yyaction(13, _, _, TokenLine) ->
    yyaction_13(TokenLine);
yyaction(14, _, _, TokenLine) ->
    yyaction_14(TokenLine);
yyaction(15, _, _, TokenLine) ->
    yyaction_15(TokenLine);
yyaction(16, _, _, TokenLine) ->
    yyaction_16(TokenLine);
yyaction(17, _, _, TokenLine) ->
    yyaction_17(TokenLine);
yyaction(18, _, _, TokenLine) ->
    yyaction_18(TokenLine);
yyaction(19, _, _, TokenLine) ->
    yyaction_19(TokenLine);
yyaction(20, _, _, TokenLine) ->
    yyaction_20(TokenLine);
yyaction(21, _, _, TokenLine) ->
    yyaction_21(TokenLine);
yyaction(22, _, _, TokenLine) ->
    yyaction_22(TokenLine);
yyaction(23, _, _, TokenLine) ->
    yyaction_23(TokenLine);
yyaction(24, _, _, TokenLine) ->
    yyaction_24(TokenLine);
yyaction(25, _, _, TokenLine) ->
    yyaction_25(TokenLine);
yyaction(26, TokenLen, YYtcs, TokenLine) ->
    TokenChars = yypre(YYtcs, TokenLen),
    yyaction_26(TokenChars, TokenLine);
yyaction(_, _, _, _) -> error.

-compile({inline,yyaction_0/2}).
-file("src/floki_selector_lexer.xrl", 16).
yyaction_0(TokenChars, TokenLine) ->
     { token, { identifier, TokenLine, TokenChars } } .

-compile({inline,yyaction_1/2}).
-file("src/floki_selector_lexer.xrl", 17).
yyaction_1(TokenChars, TokenLine) ->
     { token, { quoted, TokenLine, remove_wrapper (TokenChars) } } .

-compile({inline,yyaction_2/2}).
-file("src/floki_selector_lexer.xrl", 18).
yyaction_2(TokenChars, TokenLine) ->
     { token, { attribute_identifier, TokenLine, TokenChars } } .

-compile({inline,yyaction_3/2}).
-file("src/floki_selector_lexer.xrl", 19).
yyaction_3(TokenChars, TokenLine) ->
     { token, { TokenChars, TokenLine } } .

-compile({inline,yyaction_4/2}).
-file("src/floki_selector_lexer.xrl", 20).
yyaction_4(TokenChars, TokenLine) ->
     { token, { hash, TokenLine, unescape_inside_id_name (tail (TokenChars)) } } .

-compile({inline,yyaction_5/2}).
-file("src/floki_selector_lexer.xrl", 21).
yyaction_5(TokenChars, TokenLine) ->
     { token, { class, TokenLine, unescape_inside_class_name (tail (TokenChars)) } } .

-compile({inline,yyaction_6/1}).
-file("src/floki_selector_lexer.xrl", 22).
yyaction_6(TokenLine) ->
     { token, { pseudo_not, TokenLine } } .

-compile({inline,yyaction_7/2}).
-file("src/floki_selector_lexer.xrl", 23).
yyaction_7(TokenChars, TokenLine) ->
     { token, { pseudo, TokenLine, tail (TokenChars) } } .

-compile({inline,yyaction_8/2}).
-file("src/floki_selector_lexer.xrl", 24).
yyaction_8(TokenChars, TokenLine) ->
     { token, { pseudo_class_int, TokenLine, list_to_integer (remove_wrapper (TokenChars)) } } .

-compile({inline,yyaction_9/1}).
-file("src/floki_selector_lexer.xrl", 25).
yyaction_9(TokenLine) ->
     { token, { pseudo_class_odd, TokenLine } } .

-compile({inline,yyaction_10/1}).
-file("src/floki_selector_lexer.xrl", 26).
yyaction_10(TokenLine) ->
     { token, { pseudo_class_even, TokenLine } } .

-compile({inline,yyaction_11/2}).
-file("src/floki_selector_lexer.xrl", 27).
yyaction_11(TokenChars, TokenLine) ->
     { token, { pseudo_class_pattern, TokenLine, remove_wrapper (TokenChars) } } .

-compile({inline,yyaction_12/2}).
-file("src/floki_selector_lexer.xrl", 28).
yyaction_12(TokenChars, TokenLine) ->
     { token, { pseudo_class_quoted, TokenLine, remove_wrapper (remove_wrapper (TokenChars)) } } .

-compile({inline,yyaction_13/1}).
-file("src/floki_selector_lexer.xrl", 29).
yyaction_13(TokenLine) ->
     { token, { close_parentesis, TokenLine } } .

-compile({inline,yyaction_14/1}).
-file("src/floki_selector_lexer.xrl", 30).
yyaction_14(TokenLine) ->
     { token, { includes, TokenLine } } .

-compile({inline,yyaction_15/1}).
-file("src/floki_selector_lexer.xrl", 31).
yyaction_15(TokenLine) ->
     { token, { dash_match, TokenLine } } .

-compile({inline,yyaction_16/1}).
-file("src/floki_selector_lexer.xrl", 32).
yyaction_16(TokenLine) ->
     { token, { prefix_match, TokenLine } } .

-compile({inline,yyaction_17/1}).
-file("src/floki_selector_lexer.xrl", 33).
yyaction_17(TokenLine) ->
     { token, { suffix_match, TokenLine } } .

-compile({inline,yyaction_18/1}).
-file("src/floki_selector_lexer.xrl", 34).
yyaction_18(TokenLine) ->
     { token, { substring_match, TokenLine } } .

-compile({inline,yyaction_19/1}).
-file("src/floki_selector_lexer.xrl", 35).
yyaction_19(TokenLine) ->
     { token, { equal, TokenLine } } .

-compile({inline,yyaction_20/1}).
-file("src/floki_selector_lexer.xrl", 36).
yyaction_20(TokenLine) ->
     { token, { comma, TokenLine } } .

-compile({inline,yyaction_21/1}).
-file("src/floki_selector_lexer.xrl", 37).
yyaction_21(TokenLine) ->
     { token, { greater, TokenLine } } .

-compile({inline,yyaction_22/1}).
-file("src/floki_selector_lexer.xrl", 38).
yyaction_22(TokenLine) ->
     { token, { plus, TokenLine } } .

-compile({inline,yyaction_23/1}).
-file("src/floki_selector_lexer.xrl", 39).
yyaction_23(TokenLine) ->
     { token, { tilde, TokenLine } } .

-compile({inline,yyaction_24/1}).
-file("src/floki_selector_lexer.xrl", 40).
yyaction_24(TokenLine) ->
     { token, { namespace_pipe, TokenLine } } .

-compile({inline,yyaction_25/1}).
-file("src/floki_selector_lexer.xrl", 41).
yyaction_25(TokenLine) ->
     { token, { space, TokenLine } } .

-compile({inline,yyaction_26/2}).
-file("src/floki_selector_lexer.xrl", 42).
yyaction_26(TokenChars, TokenLine) ->
     { token, { unknown, TokenLine, TokenChars } } .

-file("c:/Program Files/Erlang OTP/lib/parsetools-2.4.1/include/leexinc.hrl", 313).
