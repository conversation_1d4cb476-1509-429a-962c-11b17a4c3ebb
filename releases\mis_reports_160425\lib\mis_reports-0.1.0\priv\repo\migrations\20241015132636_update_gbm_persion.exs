defmodule MisReports.Repo.Migrations.UpdateGbmPersion do
  use Ecto.Migration

  def up do
    # alter table(:tbl_cust_segmentation) do
    #   modify :acc_bal_in_lcy, :decimal, precision: 38, scale: 2
    #   modify :acc_bal_in_ccy, :decimal, precision: 38, scale: 2
    #   modify :avg_bal_in_lcy, :decimal, precision: 38, scale: 2
    #   modify :avg_bal_in_ccy, :decimal, precision: 38, scale: 2
    #   modify :daily_mvmt_in_ccy, :decimal, precision: 38, scale: 2
    #   modify :daily_mvmt_in_lcy, :decimal, precision: 38, scale: 2
    #   modify :mdt_mvmt_in_ccy, :decimal, precision: 38, scale: 2
    #   modify :mdt_mvmt_in_lcy, :decimal, precision: 38, scale: 2
    # end
  end

  def down do
    # alter table(:tbl_cust_segmentation) do
    #   modify :acc_bal_in_lcy, :decimal, precision: 18, scale: 2
    #   modify :acc_bal_in_ccy, :decimal, precision: 18, scale: 2
    #   modify :avg_bal_in_lcy, :decimal, precision: 18, scale: 2
    #   modify :avg_bal_in_ccy, :decimal, precision: 18, scale: 2
    #   modify :daily_mvmt_in_ccy, :decimal, precision: 18, scale: 2
    #   modify :daily_mvmt_in_lcy, :decimal, precision: 18, scale: 2
    #   modify :mdt_mvmt_in_ccy, :decimal, precision: 18, scale: 2
    #   modify :mdt_mvmt_in_lcy, :decimal, precision: 18, scale: 2
    # end
  end
end
