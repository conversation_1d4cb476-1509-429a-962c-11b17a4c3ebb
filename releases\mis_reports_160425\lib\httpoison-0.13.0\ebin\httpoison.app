{application,httpoison,
             [{applications,[kernel,stdlib,elixir,hackney]},
              {description,"  Yet Another HTTP client for Elixir powered by hackney\n"},
              {modules,['Elixir.HTTPoison','Elixir.HTTPoison.AsyncChunk',
                        'Elixir.HTTPoison.AsyncEnd',
                        'Elixir.HTTPoison.AsyncHeaders',
                        'Elixir.HTTPoison.AsyncRedirect',
                        'Elixir.HTTPoison.AsyncResponse',
                        'Elixir.HTTPoison.AsyncStatus',
                        'Elixir.HTTPoison.Base','Elixir.HTTPoison.Error',
                        'Elixir.HTTPoison.Response']},
              {registered,[]},
              {vsn,"0.13.0"}]}.
