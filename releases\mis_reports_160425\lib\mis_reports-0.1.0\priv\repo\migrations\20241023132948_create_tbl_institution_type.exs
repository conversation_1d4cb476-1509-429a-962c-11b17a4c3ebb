defmodule MisReports.Repo.Migrations.CreateTblInstitutionType do
  use Ecto.Migration

  def change do
    create table(:tbl_institution_type) do
      add :institution_name, :string
      add :institution_type, :string
      add :relationship, :string
      add :foreign_domestic, :string
      add :type_of_financial_institution, :string
      add :status, :string
      add :report_date, :date
      add :maker_id, references(:tbl_users, on_delete: :nothing)
      add :checker_id, references(:tbl_users, on_delete: :nothing)

      timestamps()
    end
  end
end
