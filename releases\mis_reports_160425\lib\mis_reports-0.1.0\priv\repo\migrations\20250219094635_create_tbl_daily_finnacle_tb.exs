defmodule MisReports.Repo.Migrations.CreateTblDailyFinnacleTb do
  use Ecto.Migration

  def change do
    create table(:tbl_daily_finnacle_tb) do
      add :report_date, :date
      add :year, :string
      add :month, :string
      add :day, :string
      add :sol_id, :string
      add :sol_desc, :string
      add :acct_code, :string
      add :acct_num, :string
      add :acct_num_padded, :string
      add :acct_name, :string
      add :sch_code, :string
      add :ccy_id, :string
      add :exch_rate, :decimal
      add :closing_bal, :decimal
      add :lcy_equiv, :decimal
      add :is_non_perf, :string
      add :is_intercompany, :string
      add :sap_gl, :string
      add :sap_gl_name, :string
      add :type, :string
      add :src_file_id, :bigint

      timestamps()
    end
  end
end
