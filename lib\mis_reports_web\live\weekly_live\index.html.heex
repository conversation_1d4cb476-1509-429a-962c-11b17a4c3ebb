<style>
  .overflow{
   width: 300px;
   height: 55%;
   overflow: auto;
   max-height: calc(100vh - 200px); /* Adjust based on your needs */
   overflow-y: auto;
   overflow-x: hidden;
   padding-right: 0.5rem;
   scrollbar-width: thin;
   scrollbar-color: #e5e7eb transparent;
   }
   .overflow::-webkit-scrollbar {
   width: 6px;
   }
   .overflow::-webkit-scrollbar-track {
   background: transparent;
   }
   .overflow::-webkit-scrollbar-thumb {
   background-color: #e5e7eb;
   border-radius: 3px;
   }
  .linkselector{
     color:#0550C4;
  }
   .dropbtn {
   background-color: none;
   color: gray;
   padding: 5px;
   font-size: 16px;
   border: none;
   }
   .dropdown {
   position: relative;
   display: inline-block;
   }
   .dropdown-content {
   display: none;
   position: absolute;
   background-color: #fff;
   min-width: 160px;
   box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
   z-index: 100;
   }
   .dropdown-content a {
   color: black;
   padding: 8px 8px;
   text-decoration: none;
   display: block;
   }
   .dropdown-content a:hover {background-color: #ddd;}
   .dropdown:hover .dropdown-content {display: block;}
   .dropdown:hover .dropbtn {background-color: none;}
   .mains {
   border-bottom: 1px solid gray;
  }
  .linkselector{
     color:#0550C4;
  }
  .approval-buttons {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background-color: white;
    padding: 1rem;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    z-index: 50;
  }

  .content-wrapper {
    padding-bottom: 80px; /* Make space for fixed buttons */
  }

  .tab-navigation {
    border-bottom: 1px solid #e5e7eb;
    margin-bottom: 1rem;
  }

  .tab-button {
    padding: 0.75rem 1.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    border-bottom: 2px solid transparent;
    margin-right: 1rem;
    transition: all 0.3s;
  }

  .tab-button.active {
    border-bottom: 2px solid #4f46e5;
    color: #4f46e5;
  }

  .tab-button:hover:not(.active) {
    color: #6b7280;
  }

  .schedule-container {
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    position: relative;
    padding-top: 4rem;  /* Make space for the action buttons */
    scrollbar-width: none;  /* Firefox */
    -ms-overflow-style: none;  /* IE and Edge */
  }

  .schedule-container::-webkit-scrollbar {
    display: none;  /* Chrome, Safari, Opera */
  }

  .fullscreen-btn {
    padding: 0.5rem;
    border-radius: 0.375rem;
    background-color: #f3f4f6;
    color: #374151;
    transition: all 0.2s;
  }

  .fullscreen-btn:hover {
    background-color: #e5e7eb;
  }


  .exit-fullscreen-btn {

    padding: 0.5rem;
    border-radius: 0.375rem;
    background-color: #f3f4f6;
    color: #374151;
    z-index: 51;
  }

  .approval-buttons {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: white;
    padding: 1rem;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    z-index: 50;
  }

  .actions-header {
    position: absolute;
    top: 1rem;
    right: 1rem;
    display: flex;
    gap: 0.75rem;
    z-index: 10;
  }

  .actions-container {
    position: absolute;
    top: 1rem;
    right: 1rem;
    display: flex;
    align-items: center;
    z-index: 50;
  }

  .button-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .fullscreen-btn {
    display: inline-flex;

    background-color: #f3f4f6;
    color: #374151;
    transition: all 0.2s;
    border: 1px solid #e5e7eb;

  }

  .fullscreen-btn:hover {
    background-color: #e5e7eb;
    color: #111827;
  }

  .btn-icon {
    width: 1.25rem;
    height: 1.25rem;
  }

  .btn-text {
    font-size: 0.875rem;
    font-weight: 500;
  }

  .comment-card {
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    margin-top: 1rem;
    position: sticky;
    bottom: 0;
    z-index: 40;
  }
/* Add to your existing style block */
html {
  scroll-behavior: smooth;
}

.scroll-mt-24 {
  scroll-margin-top: 6rem;
}
  .comment-textarea {
    width: 100%;
    min-height: 80px;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    padding: 0.75rem;
    margin-bottom: 1rem;
    resize: vertical;
  }

  .comment-actions {
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
  }



  @keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
      transform: translateY(0) translateX(50%);
    }
    40% {
      transform: translateY(-10px) translateX(50%);
    }
    60% {
      transform: translateY(-5px) translateX(50%);
    }
  }

  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
  }

  .fixed.inset-0.bg-gray-800.bg-opacity-50 {
    z-index: 99999;
  }

  .loader-overlay {
    position: fixed;
    inset: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 50;
  }

  .loader-spinner {
    width: 3rem;
    height: 3rem;
    border: 3px solid #e5e7eb;
    border-radius: 50%;
    border-top-color: #4f46e5;
    animation: spin 1s linear infinite;
  }

  .loader-text {
    margin-top: 1rem;
    color: #4f46e5;
    font-size: 0.875rem;
    font-weight: 500;
  }

  @keyframes spin {
    to { transform: rotate(360deg); }
  }

  @keyframes pulse {
    0% {
      transform: scale(1);
      opacity: 1;
    }
    50% {
      transform: scale(1.2);
      opacity: 0.7;
    }
    100% {
      transform: scale(1);
      opacity: 1;
    }
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translate(-50%, -40%);
    }
    to {
      opacity: 1;
      transform: translate(-50%, -50%);
    }
  }
.helper-message {
  position: fixed;
  left: 50%;
  bottom: 2rem;
  transform: translateX(-50%);
  background: white;
  color: #4f46e5;
  padding: 0.75rem 1.5rem;
  border-radius: 0.75rem;
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.15);
  font-size: 0.875rem;
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  z-index: 50;
  text-decoration: none;
  transition: all 0.2s ease;
  border: 1px solid #e5e7eb;
}

.helper-message:hover {
  transform: translate(-50%, -2px);
  box-shadow: 0 8px 16px rgba(79, 70, 229, 0.2);
  background: #f9fafb;
}

.helper-message svg {
  width: 1.25rem;
  height: 1.25rem;
  color: #4f46e5;
}

.helper-message span {
  font-weight: 500;
  color: #374151;
}

.helper-message-primary {
  background: linear-gradient(135deg, #4f46e5 0%, #4338ca 100%);
  color: white;
  border: none;
}

.helper-message-primary:hover {
  background: linear-gradient(135deg, #4338ca 0%, #3730a3 100%);
}

.helper-message-primary svg,
.helper-message-primary span {
  color: white;
}
@keyframes bounce {
  0%, 100% {
    transform: translateX(-50%) translateY(0);
  }
  50% {
    transform: translateX(-50%) translateY(-10px);
  }
}
.actions-container {
  position: absolute;
  top: 1rem;
  right: 1rem;
  display: flex;
  align-items: center;
  z-index: 50;
}

.button-group {
  display: flex;
  align-items: center;
  gap: 0.25rem; /* Reduced gap between buttons */
}

.action-btn, .fullscreen-btn {
 /* height: 32px;  Smaller height */
  display: inline-flex;
}

.btn-icon {
  width: 1rem; /* Smaller icons */
  height: 1rem;
}

.btn-text {
  font-size: 0.75rem;
  font-weight: 500;
}

  .fullscreen {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 9999 !important;
    margin: 0 !important;
    padding: 2rem !important;
    background: white !important;
    overflow-y: auto !important;
  }

  .schedule-container {
    position: relative;
    transition: all 0.3s ease;
  }

  .schedule-container.fullscreen {
    border-radius: 0;
    box-shadow: none;
  }


</style>

  <%# Helper Message %>
  <%= if @show_helper and not @view_only do %>
    <% step_name = MisReports.Workflow.get_wklstep_rule!(@step_id).step_name %>
    <%= case step_name do %>
      <% step when step in ["Weekly report Generation"] -> %>
        <%= if @report_type != "" && !@helper_clicked do %>
          <a href="#submit-button" class="helper-message helper-message-primary" phx-click="helper_clicked">

            <span>Click to go to the Submit Section</span>
          </a>
        <% end %>

      <% step when step in ["Weekly Return Review Stage","Weekly Return Report Approval Level 1", "Weekly Return Report Approval by CFO", "Weekly Return Report Approval by CE"] -> %>
        <%= if @report_type != "" && !@helper_clicked do %>
          <a href="#comment-section" class="helper-message" phx-click="helper_clicked">

            <span>Click to go to the Review Section</span>
          </a>
        <% end %>
    <% end %>
  <% end %>

<div class="grid grid-cols-1 lg:grid-cols-4 gap-6 p-6">
  <!-- Left Column - Main Content -->
  <div class="lg:col-span-3">

    <div class={"schedule-container #{if @is_fullscreen, do: 'fullscreen'}"} id="schedule-content">
      <%= if @report_type == "" do %>
        <!-- Simple Date Filter -->
            <div class="space-y-10 divide-y divide-gray-900/10">
              <div class="grid grid-cols-1 gap-x-8 gap-y-8 pt-10 md:grid-cols-3">
                <div class="px-4 sm:px-0">
            <h2 class="text-base font-semibold leading-7 text-gray-900">New Weekly Report</h2>
            <p class="mt-1 text-sm leading-6 text-gray-600">New Reports  includes report date.</p>
          </div>
            <form class="bg-white shadow-sm ring-1 ring-gray-900/5 sm:rounded-xl md:col-span-2" phx-submit="filter-report">
              <div class="px-4 py-6 sm:p-8">
                <div class="grid max-w-2xl grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-2">
                  <div class="sm:col-span-2">
                    <div class="mt-2">
                      <input type="date"
                             name="report[date]"
                             value={@filter_params["date"]}
                             class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                             required />
                    </div>
                  </div>
                </div>
              </div>

              <div class="flex items-center justify-end gap-x-6 border-t border-gray-900/10 px-4 py-4 sm:px-8">
                <button type="submit" class="rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500">
                  Generate Report
                </button>

              </div>
            </form>
          </div>
        </div>
      <% else %>
        <!-- Action Buttons -->
        <div class="actions-container">
          <div class="row button-group">
           <%!-- <%= if not @view_only do %>
              <% step_name = normalize_step_name(MisReports.Workflow.get_wklstep_rule!(@step_id).step_name) %>
              <%= cond do %>
                <% Enum.member?(["Weekly report Generation"], step_name) -> %>
                  <div class="col">
                    <button
                      type="button"
                      phx-click="create_adjustment"
                      class="action-btn rounded-md bg-green-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-green-500"
                      title="Make Adjustment"
                    >
                      <svg class="btn-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                      <span class="btn-text">Make Adjustments</span>
                    </button>
                  </div>

                <% Enum.member?(@review_steps, step_name) -> %>
                  <div class="col">
                    <a
                      href={Routes.adjustments_index_path(@socket, :update_status, %{
                        reference: @reference,
                        process_id: @process_id,
                        step_id: @step_id
                      })}
                      target="_blank"
                      rel="noopener noreferrer"
                      class="action-btn rounded-md bg-blue-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-700 inline-flex items-center"
                      title="View Adjustment"
                    >
                      <svg class="btn-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                      <span class="btn-text">View Adjustments</span>
                    </a>
                  </div>

                <% true -> %>
                  <%# Default case %>
              <% end %>
            <% end %> --%>

            <%= if @data do %>
              <div class="col">
                <%= case @process_id do %>
                  <% "3000" -> %> <%# Forex Risk %>
                    <button
                      type="button"
                      phx-click="export_fx"
                      class="action-btn rounded-md bg-blue-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 mr-2"
                      title="Export Forex Risk Report"
                      id="export-fx-button"
                      disabled={@loader && assigns[:loader_message] == "Exporting..."}
                    >
                      <%= if @loader && assigns[:loader_message] == "Exporting..." do %>
                        <div class="flex items-center">
                          <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          <span class="btn-text">Processing...</span>
                        </div>
                      <% else %>
                        <svg class="btn-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                        </svg>
                        <span class="btn-text">Export FX Report</span>
                      <% end %>
                    </button>

                  <% "2000" -> %> <%# Core Liquid Assets %>
                    <button
                      type="button"
                      phx-click="export_cla"
                      class="action-btn rounded-md bg-blue-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 mr-2"
                      title="Export Core Liquid Assets Report"
                      id="export-cla-button"
                      disabled={@loader && assigns[:loader_message] == "Exporting..."}
                    >
                      <%= if @loader && assigns[:loader_message] == "Exporting..." do %>
                        <div class="flex items-center">
                          <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          <span class="btn-text">Processing...</span>
                        </div>
                      <% else %>
                        <svg class="btn-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                        </svg>
                        <span class="btn-text">Export CLA Report</span>
                      <% end %>
                    </button>

                  <% _ -> %> <%# Default case %>
                    <button
                      type="button"
                      phx-click="export_excel"
                      class="action-btn rounded-md bg-blue-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 mr-2"
                      title="Export to Excel"
                      id="export-button"
                      disabled={@loader && assigns[:loader_message] == "Exporting..."}
                    >
                      <%= if @loader && assigns[:loader_message] == "Exporting..." do %>
                        <div class="flex items-center">
                          <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          <span class="btn-text">Processing...</span>
                        </div>
                      <% else %>
                        <svg class="btn-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                        </svg>
                        <span class="btn-text">Export</span>
                      <% end %>
                    </button>
                <% end %>

                <%= if @reference do %>
                  <%= case @process_id do %>
                    <% "3000" -> %> <%# Forex Risk %>
                      <%= if @export_fx_filename do %>
                        <a
                          href={Routes.reports_path(@socket, :download_weekly_fx, @export_fx_filename)}
                          class="action-btn rounded-md bg-blue-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500"
                          title="Download FX Report"
                          target="_blank"
                        >
                          <svg class="btn-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0l-4 4m4-4v12" />
                          </svg>
                          <span class="btn-text">Download FX Report</span>
                        </a>
                      <% else %>
                        <a
                          href={Routes.reports_path(@socket, :download_weekly_by_reference, @reference)}
                          class="action-btn rounded-md bg-blue-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500"
                          title="Direct Download"
                          target="_blank"
                        >
                          <svg class="btn-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0l-4 4m4-4v12" />
                          </svg>
                          <span class="btn-text">Direct Download</span>
                        </a>
                      <% end %>

                    <% "2000" -> %> <%# Core Liquid Assets %>
                      <%= if @export_cla_filename do %>
                        <a
                          href={Routes.reports_path(@socket, :download_weekly_cla, @export_cla_filename)}
                          class="action-btn rounded-md bg-blue-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500"
                          title="Download CLA Report"
                          target="_blank"
                        >
                          <svg class="btn-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0l-4 4m4-4v12" />
                          </svg>
                          <span class="btn-text">Download CLA Report</span>
                        </a>
                      <% else %>
                        <a
                          href={Routes.reports_path(@socket, :download_weekly_by_reference, @reference)}
                          class="action-btn rounded-md bg-blue-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500"
                          title="Direct Download"
                          target="_blank"
                        >
                          <svg class="btn-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0l-4 4m4-4v12" />
                          </svg>
                          <span class="btn-text">Direct Download</span>
                        </a>
                      <% end %>

                    <% _ -> %> <%# Default case %>
                      <a
                        href={Routes.reports_path(@socket, :download_weekly_by_reference, @reference)}
                        class="action-btn rounded-md bg-blue-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500"
                        title="Direct Download"
                        target="_blank"
                      >
                        <svg class="btn-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0l-4 4m4-4v12" />
                        </svg>
                        <span class="btn-text">Direct Download</span>
                      </a>
                  <% end %>
                <% end %>
              </div>
            <% end %>



            <div class="col">
              <button
                type="button"
                phx-click="toggle_fullscreen"
                class="fullscreen-btn"
                title={if @is_fullscreen, do: "Exit Fullscreen", else: "Fullscreen"}
              >
                <%= if @is_fullscreen do %>
                  <svg class="btn-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V9m0 10H5m4 0l-4-4m11-1V5m0 10h4m-4 0l4-4" />
                  </svg>
                  <span class="btn-text">Exit</span>
                <% else %>
                  <svg class="btn-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 10h4m-4 0l-5-5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
                  </svg>
                  <span class="btn-text">Fullscreen</span>
                <% end %>
              </button>
            </div>
          </div>
        </div>

        <!-- Tab Navigation -->
        <div class="mb-8 px-4">
          <div class="flex items-center space-x-3 border-b border-gray-200 pb-4">

            <div>
              <h2 class="text-xl font-semibold text-gray-900 leading-6">
                <%= case @process_id do
                  "3000" -> "Forex Risk"
                  "2000" -> "Core Liquid Assets"
                  _ -> "Weekly Report"
                end %>
              </h2>

            </div>
          </div>
        </div>

        <!-- Content Area -->
        <%= Phoenix.View.render(MisReportsWeb.WeeklyView, "#{@report_type}.html", assigns) %>
      <% end %>
    </div>
  </div>

  <!-- Right Column - Schedule Navigation -->
  <%= if @report_type != "" do %>
    <div class="lg:col-span-1">
      <div class="schedule-container">
        <div class="mb-4">
          <h3 class="font-medium text-gray-900">Available Schedules</h3>
        </div>
        <div class="overflow">
          <%= render_navigation_menu(%{
            report_type: @report_type,
            current_view: @current_view,
            process_id: @process_id,
            view_only: @view_only,
            pending_comments: @pending_comments
          }) %>
        </div>
      </div>
    </div>

    <!-- Comments History Section -->


    <!-- Comments Section -->
    <div class="lg:col-span-4">
      <%= if normalize_step_name(MisReports.Workflow.get_wklstep_rule!(@step_id).step_name) in @review_steps and @report_type != "" and not @view_only do %>
        <div class="comments-history mb-4">
          <div class="bg-white rounded-lg shadow p-4">
            <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center gap-2">
              <svg class="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
              </svg>
              Review History
            </h3>

            <%= if length(@comments) > 0 do %>
              <div class="space-y-4">
                <%= for %{action_id: action_id, comments: comments, created_at: created_at, step_id: step_id, user_name: user_name} <- @comments do %>
                  <div class="flex gap-4 p-3 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors">
                    <div class="flex-shrink-0">
                      <div class="w-8 h-8 rounded-full bg-indigo-100 flex items-center justify-center">
                        <span class="text-sm font-medium text-indigo-600">
                          <%= String.slice(user_name || "", 0..1) %>
                        </span>
                      </div>
                    </div>
                    <div class="flex-1">
                      <div class="flex items-center justify-between mb-1">
                        <span class="text-sm font-medium text-gray-900">
                          <%= user_name %>
                        </span>
                        <span class="text-xs text-gray-500">
                          <%= NaiveDateTime.to_string(created_at) %>
                        </span>
                      </div>
                      <p class="text-sm text-gray-600"><%= format_comment_with_bold_schedules(comments) %></p>
                      <div class="mt-2 flex items-center gap-2">
                        <span class={"text-xs px-2 py-1 rounded-full #{
                          case action_id do
                            81 -> "bg-blue-100 text-blue-700"
                            80 -> "bg-yellow-100 text-yellow-700"
                            97 -> "bg-green-100 text-green-700"
                            96 -> "bg-red-100 text-red-700"
                            _ -> "bg-gray-100 text-gray-700"
                          end
                        }"}>
                          <%= case action_id do
                            81 -> "Initiated"
                            80 -> "Submitted"
                            97 -> "Approved"
                            96 -> "Rejected"
                            _ -> "Action #{action_id}"
                          end %>
                        </span>
                        <span class="text-xs text-gray-500">Step <%= MisReports.Workflow.get_wklstep_rule!(step_id).step_name %></span>
                      </div>
                    </div>
                  </div>
                <% end %>
              </div>
            <% else %>
              <p class="text-sm text-gray-500 text-center py-4">No comments history available</p>
            <% end %>
          </div>
        </div>
      <% end %>

      <!-- Comment Card -->
      <div id="comment-section" class="comment-card scroll-mt-24 scroll-target">
        <%= if not @view_only do %>
          <%= case MisReports.Workflow.get_wklstep_rule!(@step_id).step_name do %>
              <% step -> %>
                <%= cond do %>
                  <% step == "Weekly report Generation" -> %>
                    <div class="flex justify-end">
                      <button id="submit-button"
                        type="submit"
                        phx-click="submit_report"
                        class="scroll-target rounded-md bg-indigo-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500"
                      >
                        Submit Weekly Return
                      </button>
                    </div>

                  <% Enum.member?(@review_steps, normalize_step_name(step)) -> %>
                    <form phx-submit="save" class="comment-form">
                      <div class="mb-4">
                        <label for="comment" class="block text-sm font-medium text-gray-700 mb-2">
                          Review Comments
                          <%= if @comment && String.trim(@comment) != "" do %>
                            <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              <%= count_comments(@comment) %> schedule comment(s) included
                            </span>
                          <% end %>
                        </label>
                        <div class="relative">
                          <textarea
                            id="comment"
                            name="comment"
                            class="comment-textarea"
                            placeholder="Schedule-specific comments will appear here automatically. You can add additional review comments..."
                            rows="8"
                            required
                            phx-hook="CommentTextarea"
                            data-comment={@comment || ""}
                          ><%= @comment || "" %></textarea>

                          <!-- Debug info -->
                          <%!-- <div class="mt-1 text-xs text-gray-500">
                            Debug: Comment length = <%= String.length(@comment || "") %> characters
                            <%= if @comment && String.trim(@comment) != "" do %>
                              | Preview: <%= String.slice(@comment, 0, 50) %>...
                            <% end %>
                          </div> --%>
                          <%= if @comment && String.trim(@comment) != "" do %>
                            <div class="absolute top-2 right-2">
                              <span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-green-100 text-green-800">
                                Auto-aggregated
                              </span>
                            </div>
                          <% end %>
                        </div>
                        <%= if @comment && String.trim(@comment) != "" do %>
                          <div class="mt-2 text-xs text-gray-600">
                            <strong>Note:</strong> This field contains automatically aggregated comments from individual schedules.
                            You can add additional review comments above the existing content.
                          </div>
                        <% end %>
                      </div>
                      <div class="comment-actions">
                        <button
                          type="submit"
                          name="action"
                          value="97"
                          class="rounded-md bg-green-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-green-500"
                        >
                          Approve
                        </button>
                        <button
                          type="submit"
                          name="action"
                          value="96"
                          class="rounded-md bg-red-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-500"
                        >
                          Reject
                        </button>
                      </div>
                    </form>

                  <% true -> %>
                    <%# Default case, nothing to display %>
                <% end %>
            <% end %>
        <% end %>
      </div>
    </div>
  <% end %>
</div>

<%= if @show_cell_values && length(@cell_values) > 0 do %>
  <div class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="cell-values-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <!-- Background overlay -->
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>

      <!-- Modal panel -->
      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <div class="sm:flex sm:items-start">
            <div class="mt-3 text-center sm:mt-0 sm:text-left w-full">
              <div class="flex justify-between items-center">
                <h3 class="text-lg leading-6 font-medium text-gray-900" id="cell-values-title">
                  Cell Values Being Populated
                </h3>
                <button
                  type="button"
                  phx-click="toggle_cell_values"
                  class="rounded-md text-gray-400 hover:text-gray-500 focus:outline-none"
                >
                  <span class="sr-only">Close</span>
                  <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <div class="mt-4 max-h-96 overflow-y-auto">
                <div class="flex justify-between mb-2">
                  <div class="text-sm font-medium text-gray-700">
                    Total cells: <%= length(@cell_values) %>
                  </div>
                  <div class="text-sm text-gray-500">
                    Showing the actual values being written to the Excel file
                  </div>
                </div>

                <table class="min-w-full divide-y divide-gray-200">
                  <thead class="bg-gray-50">
                    <tr>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sheet</th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cell</th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Value</th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Source</th>
                    </tr>
                  </thead>
                  <tbody class="bg-white divide-y divide-gray-200">
                    <%= for cell <- Enum.take(@cell_values, 100) do %>
                      <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"><%= cell.sheet %></td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><%= cell.cell %></td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><%= cell.type %></td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><%= inspect(cell.value) %></td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <%= if Map.has_key?(cell, :source_cell) do %>
                            <%= cell.source_cell %>
                          <% else %>
                            <%= if Map.has_key?(cell, :source_value), do: cell.source_value, else: "-" %>
                          <% end %>
                        </td>
                      </tr>
                    <% end %>

                    <%= if length(@cell_values) > 100 do %>
                      <tr>
                        <td colspan="5" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                          Showing 100 of <%= length(@cell_values) %> cells. Too many to display all.
                        </td>
                      </tr>
                    <% end %>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
        <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
          <button
            type="button"
            phx-click="toggle_cell_values"
            class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  </div>
<% end %>

<%= if @loader do %>
  <div class="loader-overlay">
    <div class="loader-spinner"></div>
    <p class="loader-text">
      <%= if assigns[:loader_message] do %>
        <%= @loader_message %>
      <% else %>
        Loading schedule...
      <% end %>
    </p>
    <p class="text-sm text-gray-500 mt-2">
      <%= cond do %>
        <% assigns[:loader_message] == "Exporting..." -> %>
          Please wait while we generate your report. This may take a few moments...
        <% assigns[:loader_message] -> %>
          Please wait while we prepare your file...
        <% @report_type == "" -> %>
          Please wait...
        <% true -> %>
          Loading <%= String.replace(@report_type, "_", " ") %>...
      <% end %>
    </p>
  </div>
<% end %>

<.confirm_modal />
<.info_notification />
<.error_notification />

<script>
  function hideCommentModal() {
    const modal = document.getElementById('comment-modal');
    if (modal) {
      modal.style.display = 'none';
      modal.style.visibility = 'hidden';
      modal.classList.add('hidden');
    }
    return true; // Allow form submission to continue
  }

  // Add debugging function for the page
  function debugModal() {
    console.log('=== MODAL DEBUG FROM PAGE ===');
    const modal = document.getElementById('comment-modal');
    console.log('Modal element:', modal);
    if (modal) {
      console.log('Modal computed style:', window.getComputedStyle(modal));
      console.log('Modal display:', modal.style.display);
      console.log('Modal visibility:', modal.style.visibility);
    }

    const commentButtons = document.querySelectorAll('[phx-click="open_comment_modal"]');
    console.log('Comment buttons:', commentButtons.length);

    console.log('=== END MODAL DEBUG ===');
  }

  // Make debug function available globally
  window.debugModal = debugModal;

  // **TEST FUNCTION FOR COMMENT AGGREGATION**
  window.testCommentAggregation = function() {
    console.log('=== TESTING COMMENT AGGREGATION ===');

    const mainCommentField = document.getElementById('comment');
    console.log('Main comment field found:', !!mainCommentField);

    if (mainCommentField) {
      console.log('Current value:', mainCommentField.value);
      console.log('Current data-comment:', mainCommentField.dataset.comment);

      // Test updating the field
      const testComment = '[Schedule 27] - Test comment from JavaScript';
      mainCommentField.value = testComment;
      mainCommentField.dataset.comment = testComment;

      console.log('Updated value to:', mainCommentField.value);

      // Trigger visual feedback
      mainCommentField.style.backgroundColor = '#fef3c7';
      mainCommentField.style.border = '2px solid #f59e0b';
      setTimeout(() => {
        mainCommentField.style.backgroundColor = '';
        mainCommentField.style.border = '';
      }, 2000);
    }

    console.log('=== END TEST ===');
  };

  // **MANUAL TEXTAREA UPDATE FUNCTION**
  window.forceUpdateCommentTextarea = function() {
    console.log('=== FORCING TEXTAREA UPDATE ===');

    if (window.commentTextareaHook) {
      console.log('Found textarea hook, calling updateTextarea()');
      window.commentTextareaHook.updateTextarea();
    } else {
      console.log('No textarea hook found, trying direct update');
      const mainCommentField = document.getElementById('comment');
      if (mainCommentField && mainCommentField.dataset.comment) {
        mainCommentField.value = mainCommentField.dataset.comment;
        console.log('Updated textarea value directly');
      }
    }

    console.log('=== END FORCE UPDATE ===');
  };

  // Listen for the hide-comment-modal event from LiveView
  window.addEventListener('phx:hide-comment-modal', function(e) {
    console.log('Received hide-comment-modal event');
    hideCommentModal();
  });

  // **AUTOMATIC MAIN COMMENT FIELD UPDATE - ENHANCED**
  // Listen for comment updates from LiveView
  window.addEventListener('phx:update-main-comment', function(e) {
    console.log('Received update-main-comment event:', e.detail);
    const mainCommentField = document.getElementById('comment');
    if (mainCommentField && e.detail.comment) {
      // Force update the textarea value and content
      mainCommentField.value = e.detail.comment;
      mainCommentField.textContent = e.detail.comment;
      mainCommentField.innerHTML = e.detail.comment;

      // Store for future reference
      window.lastAggregatedComment = e.detail.comment;
      console.log('Main comment field updated automatically with aggregated comments:', e.detail.comment);

      // Trigger a visual indication that the field was updated
      mainCommentField.style.backgroundColor = '#f0f9ff';
      mainCommentField.style.border = '2px solid #3b82f6';
      setTimeout(() => {
        mainCommentField.style.backgroundColor = '';
        mainCommentField.style.border = '';
      }, 2000);

      // Dispatch a custom event to notify other parts of the page
      mainCommentField.dispatchEvent(new Event('input', { bubbles: true }));
      mainCommentField.dispatchEvent(new Event('change', { bubbles: true }));
    }
  });

  // Also listen for the custom event
  document.addEventListener('DOMContentLoaded', function() {
    const modal = document.getElementById('comment-modal');
    if (modal) {
      // Listen for form submission to hide modal
      const form = modal.querySelector('#comment-form');
      if (form) {
        form.addEventListener('submit', function(e) {
          console.log('Form submitted, hiding modal in 100ms');
          setTimeout(() => {
            hideCommentModal();
          }, 100);
        });
      }
    }

    // **REAL-TIME COMMENT SYNCHRONIZATION**
    // Listen for LiveView updates to sync the main comment field
    window.addEventListener('phx:update', function() {
      const mainCommentField = document.getElementById('comment');
      if (mainCommentField && window.lastAggregatedComment) {
        // Update the main comment field with aggregated comments
        mainCommentField.value = window.lastAggregatedComment;
        console.log('Main comment field updated with aggregated comments');
      }
    });
  });
</script>

<style>
  /* Ensure comment modal is properly styled and visible when shown */
  #comment-modal {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    z-index: 9999 !important;
  }

  #comment-modal[style*="display: block"] {
    display: block !important;
    visibility: visible !important;
  }

  /* Ensure modal content is centered and visible */
  #comment-modal .inline-block {
    position: relative !important;
    z-index: 10000 !important;
  }
</style>

<div
  id="comment-modal"
  class={"fixed inset-0 z-50 overflow-y-auto #{if @show_comment_modal, do: "", else: "hidden"}"}
  aria-labelledby="modal-title"
  role="dialog"
  aria-modal="true"
  data-reference={@reference}
>
  <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
    <!-- Background overlay -->
    <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>

    <!-- Modal panel -->
    <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
      <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
        <div class="sm:flex sm:items-start">
          <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
            <!-- Comment icon -->
            <svg class="h-6 w-6 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
            </svg>
          </div>
          <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
            <%
              # Check if this schedule has an existing comment
              has_existing_comment = @pending_comments && @current_schedule && Map.has_key?(@pending_comments, @current_schedule)
              modal_title = if has_existing_comment, do: "Edit Comment", else: "Add Comment"
            %>
            <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
               <%= modal_title %> on <%= @current_schedule_name || "Schedule" %>
               <%= if has_existing_comment do %>
                 <span class="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                   Editing
                 </span>
               <% end %>
            </h3>
            <div class="mt-4">
              <form id="comment-form" phx-submit="save_comment">
                <div>
                  <label for="comment" class="block text-sm font-medium text-gray-700">
                    Comment for <%= @current_schedule_name || "Schedule" %>
                  </label>
                  <div class="mt-1">
                    <textarea
                      id="modal-comment"
                      name="comment"
                      rows="4"
                      class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                      placeholder="Enter your comment here..."
                      value={@modal_comment}
                      required
                    ></textarea>
                  </div>
                  <%= if @comment && String.trim(@comment) != "" do %>
                    <div class="mt-2 p-2 bg-gray-50 rounded-md">
                      <p class="text-xs text-gray-600 mb-1">
                        Existing comments (<%= count_comments(@comment) %> total):
                      </p>
                      <div class="text-xs text-gray-700 max-h-20 overflow-y-auto">
                        <%= for line <- String.split(@comment, "\n") do %>
                          <%= if String.trim(line) != "" do %>
                            <div class="mb-1"><%= line %></div>
                          <% end %>
                        <% end %>
                      </div>
                    </div>
                  <% end %>
                </div>
                <div class="mt-4 flex justify-end">
                  <button
                    type="button"
                    class="mr-3 inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:w-auto sm:text-sm"
                    phx-click="close_comment_modal"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    class="inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
                  >
                    <%= if has_existing_comment, do: "Update Comment", else: "Save Comment" %>
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
