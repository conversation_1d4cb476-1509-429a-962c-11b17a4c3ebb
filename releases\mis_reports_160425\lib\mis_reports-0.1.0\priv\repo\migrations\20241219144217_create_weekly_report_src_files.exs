defmodule MisReports.Repo.Migrations.CreateWeeklyReportSrcFiles do
  use Ecto.Migration

  def change do
    create table(:weekly_report_src_files) do
      add :filename, :string
      add :status, :string
      add :date, :date
      add :month, :string
      add :year, :string
      add :maker_id, references(:tbl_users, on_delete: :nothing)
      add :checker_id, references(:tbl_users, on_delete: :nothing)

      timestamps()
    end

    create index(:weekly_report_src_files, [:maker_id])
    create index(:weekly_report_src_files, [:checker_id])
  end
end
