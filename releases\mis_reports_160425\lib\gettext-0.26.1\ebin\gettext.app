{application,gettext,
             [{applications,[kernel,stdlib,elixir,logger,expo]},
              {description,"Internationalization and localization through gettext"},
              {modules,['Elixir.Gettext','Elixir.Gettext.Application',
                        'Elixir.Gettext.Backend','Elixir.Gettext.Compiler',
                        'Elixir.Gettext.Error','Elixir.Gettext.Extractor',
                        'Elixir.Gettext.ExtractorAgent',
                        'Elixir.Gettext.Fuzzy','Elixir.Gettext.Interpolation',
                        'Elixir.Gettext.Interpolation.Default',
                        'Elixir.Gettext.Macros','Elixir.Gettext.Merger',
                        'Elixir.Gettext.MissingBindingsError',
                        'Elixir.Gettext.Plural',
                        'Elixir.Gettext.Plural.UnknownLocaleError',
                        'Elixir.Gettext.PluralFormError',
                        'Elixir.Mix.Tasks.Compile.Gettext',
                        'Elixir.Mix.Tasks.Gettext.Extract',
                        'Elixir.Mix.Tasks.Gettext.Merge']},
              {registered,[]},
              {vsn,"0.26.1"},
              {env,[{default_locale,<<"en">>},
                    {plural_forms,'Elixir.Gettext.Plural'}]},
              {mod,{'Elixir.Gettext.Application',[]}}]}.
