defmodule MisReports.Repo.Migrations.CreateWeeklyReportEntriesAllDeal do
  use Ecto.Migration

  def change do
    create table(:weekly_report_entries_all_deal) do
      add :date, :string
      add :country, :string
      add :prod_type, :string
      add :trade_id, :string
      add :b_s, :string
      add :prod_desc, :string
      add :prod_cate, :string
      add :trade_ccy, :string
      add :accbok, :string
      add :book, :string
      add :mirror_book, :string
      add :trade_dt, :string
      add :setlemt_dt, :string
      add :maturity_dt, :string
      add :total_tenor, :integer
      add :days_accted_for, :integer
      add :days_left, :integer
      add :prev_coupon, :string
      add :nxt_coupon, :string
      add :coupon, :decimal, precision: 18, scale: 2
      add :issuer, :string
      add :counter_party, :string
      add :counter_party_grp, :string
      add :trad_price, :decimal, precision: 18, scale: 2
      add :yield, :decimal, precision: 18, scale: 2
      add :market_yield, :decimal, precision: 18, scale: 2
      add :fx_rate, :decimal, precision: 18, scale: 2
      add :open_quantity, :decimal, precision: 18, scale: 2
      add :orig_nominal, :decimal, precision: 18, scale: 2
      add :open_nominal, :decimal, precision: 18, scale: 2
      add :open_nominal_base, :decimal, precision: 18, scale: 2
      add :orig_clean_setle, :decimal, precision: 18, scale: 2
      add :clean_setlemt_amt, :decimal, precision: 18, scale: 2
      add :clean_setlemt_base, :decimal, precision: 18, scale: 2
      add :unsettled, :decimal, precision: 18, scale: 2
      add :coupon_accrual, :decimal, precision: 18, scale: 2
      add :realised_premium, :decimal, precision: 18, scale: 2
      add :cary_val, :decimal, precision: 18, scale: 2
      add :mtm, :decimal, precision: 18, scale: 2
      add :adjustment, :decimal, precision: 18, scale: 2
      add :far_prmary_amt, :decimal, precision: 18, scale: 2
      add :counter_party_flag, :string
      add :far_sec_amt, :decimal, precision: 18, scale: 2
      add :fx_far_all_rate, :decimal, precision: 18, scale: 2
      add :fx__near_rate, :decimal, precision: 18, scale: 2
      add :fx_far_date, :string
      add :payment_freq, :string
      add :primary_ccy, :string
      add :primary_amt, :decimal, precision: 18, scale: 2
      add :trans_fam, :string
      add :sec_ccy, :string
      add :sec_amt, :decimal, precision: 18, scale: 2
      add :reset_dt, :string
      add :rate_index_spread, :integer
      add :acc_class, :string
      add :src_filename, :string
      add :isin, :string

      timestamps()
    end
  end
end
