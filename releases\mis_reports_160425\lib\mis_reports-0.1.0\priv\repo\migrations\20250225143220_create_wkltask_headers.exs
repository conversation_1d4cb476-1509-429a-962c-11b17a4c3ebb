defmodule MisReports.Repo.Migrations.CreateWkltaskHeaders do
  use Ecto.Migration

  def change do
    create table(:wkltask_headers) do
      add :header_id, :integer
      add :reference_number, :string
      add :process_id, :integer
      add :start_date, :date
      add :end_date, :date
      add :previous_reference_number, :string
      add :started_by, :string

      timestamps()
    end
  end
end
