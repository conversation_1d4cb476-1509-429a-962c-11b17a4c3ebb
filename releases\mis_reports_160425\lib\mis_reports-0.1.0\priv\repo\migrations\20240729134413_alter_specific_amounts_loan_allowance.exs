defmodule MisReports.Repo.Migrations.AlterSpecificAmountsLoanAllowance do
  use Ecto.Migration

  def up do
    alter table(:tblm_allowances) do
      modify(:specific_bal_allowaance, :decimal, precision: 18, scale: 2)
      modify(:specific_add_provision, :decimal, precision: 18, scale: 2)
      modify(:specific_exchange_difference, :decimal, precision: 18, scale: 2)
    end
  end

  def down do

  end
end
