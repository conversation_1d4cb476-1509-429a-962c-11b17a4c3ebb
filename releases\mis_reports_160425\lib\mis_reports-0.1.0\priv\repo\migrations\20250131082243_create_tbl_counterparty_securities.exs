defmodule MisReports.Repo.Migrations.CreateTblCounterpartySecurities do
  use Ecto.Migration

  def change do
    create table(:tbl_counterparty_securities) do
      add :report_date, :date
      add :security_type, :string
      add :issuer_name, :string
      add :maker_id, references(:tbl_users, on_delete: :nothing)
      add :checker_id, references(:tbl_users, on_delete: :nothing)


      timestamps()
    end
  end
end
