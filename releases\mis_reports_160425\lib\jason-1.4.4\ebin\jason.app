{application,jason,
             [{applications,[kernel,stdlib,elixir]},
              {description,"A blazing fast JSON parser and generator in pure Elixir.\n"},
              {modules,['Elixir.Enumerable.Jason.OrderedObject',
                        'Elixir.Jason','Elixir.Jason.Codegen',
                        'Elixir.Jason.DecodeError','Elixir.Jason.Decoder',
                        'Elixir.Jason.Decoder.Unescape','Elixir.Jason.Encode',
                        'Elixir.Jason.EncodeError','Elixir.Jason.Encoder',
                        'Elixir.Jason.Encoder.Any',
                        'Elixir.Jason.Encoder.Atom',
                        'Elixir.Jason.Encoder.BitString',
                        'Elixir.Jason.Encoder.Date',
                        'Elixir.Jason.Encoder.DateTime',
                        'Elixir.Jason.Encoder.Decimal',
                        'Elixir.Jason.Encoder.Float',
                        'Elixir.Jason.Encoder.Integer',
                        'Elixir.Jason.Encoder.Jason.Fragment',
                        'Elixir.<PERSON>.Encoder.Jason.OrderedObject',
                        'Elixir.Jason.Encoder.List',
                        'Elixir.Jason.Encoder.Map',
                        'Elixir.Jason.Encoder.NaiveDateTime',
                        'Elixir.Jason.Encoder.Time','Elixir.<PERSON>.Formatter',
                        'Elixir.<PERSON>.Fragment','Elixir.Jason.Helpers',
                        'Elixir.Jason.OrderedObject','Elixir.Jason.Sigil']},
              {registered,[]},
              {vsn,"1.4.4"}]}.
