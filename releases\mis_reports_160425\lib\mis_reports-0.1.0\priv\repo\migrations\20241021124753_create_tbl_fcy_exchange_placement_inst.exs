defmodule MisReports.Repo.Migrations.CreateTblFcyExchangePlacementInst do
  use Ecto.Migration

  def change do
    create table(:tbl_fcy_exchange_placement_inst) do
      add :institution_name, :string
      add :rating, :string
      add :rating_agency, :string
      add :maximum, :string
      add :report_date, :date
      add :maker_id, references(:tbl_users, on_delete: :nothing)
      add :checker_id, references(:tbl_users, on_delete: :nothing)

      timestamps()
    end
  end
end
