defmodule MisReports.Repo.Migrations.CreateWklprocesses do
  use Ecto.Migration

  def change do
    create table(:wklprocesses) do
      add :moduleid, :integer
      add :name, :string
      add :process_id, :integer
      add :description, :text
      add :starting_step, :integer
      add :created_by, :string
      add :created_date, :date
      add :modified_by, :string
      add :modified_date, :date

      timestamps()
    end
  end
end
