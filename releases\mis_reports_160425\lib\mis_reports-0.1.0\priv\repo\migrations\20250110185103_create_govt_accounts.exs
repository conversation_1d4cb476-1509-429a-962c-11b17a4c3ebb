defmodule MisReports.Repo.Migrations.CreateGovtAccounts do
  use Ecto.Migration

  def change do
    create table(:govt_accounts) do
      add :account_name, :string
      add :account_number, :string
      add :product_code_source, :string
      add :maker_id, references(:tbl_users, on_delete: :nothing)
      add :checker_id, references(:tbl_users, on_delete: :nothing)

      timestamps()
    end
  end
end
