{application,expo,
             [{applications,[kernel,stdlib,elixir]},
              {description,"Low-level Gettext file handling (.po/.pot/.mo file writer and parser)."},
              {modules,['Elixir.Expo.MO','Elixir.Expo.MO.Composer',
                        'Elixir.Expo.MO.InvalidFileError',
                        'Elixir.Expo.MO.Parser',
                        'Elixir.Expo.MO.UnsupportedVersionError',
                        'Elixir.Expo.Message','Elixir.Expo.Message.Plural',
                        'Elixir.Expo.Message.Singular','Elixir.Expo.Messages',
                        'Elixir.Expo.PO','Elixir.Expo.PO.Composer',
                        'Elixir.Expo.PO.DuplicateMessagesError',
                        'Elixir.Expo.PO.Parser','Elixir.Expo.PO.SyntaxError',
                        'Elixir.Expo.PO.Tokenizer','Elixir.Expo.PluralForms',
                        'Elixir.Expo.PluralForms.Known',
                        'Elixir.Expo.PluralForms.SyntaxError',
                        'Elixir.Expo.PluralForms.Tokenizer',
                        'Elixir.Expo.Util',
                        'Elixir.Inspect.Expo.Message.Plural',
                        'Elixir.Inspect.Expo.Message.Singular',
                        'Elixir.Inspect.Expo.PluralForms',
                        'Elixir.Mix.Tasks.Expo.Msgfmt',
                        'Elixir.Mix.Tasks.Expo.Msguniq',
                        expo_plural_forms_parser,expo_po_parser]},
              {registered,[]},
              {vsn,"1.1.0"}]}.
