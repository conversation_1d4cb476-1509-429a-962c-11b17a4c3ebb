{application,mis_reports,
    [{compile_env,[{mis_reports,['Elixir.MisReportsWeb.Gettext'],error}]},
     {applications,
         [kernel,stdlib,elixir,logger,runtime_tools,phoenix,phoenix_ecto,
          ecto_sql,tds,phoenix_html,phoenix_live_reload,phoenix_live_view,
          esbuild,telemetry_metrics,telemetry_poller,gettext,decimal,jason,
          plug_cowboy,httpoison,poison,atomic_map,scrivener,scrivener_ecto,
          timex,calendar,mime,bamboo,bamboo_smtp,number,csv,quantum,
          logger_file_backend,elixlsx,pipe_to,ex_crypto,ex_phone_number,phone,
          sleeplocks,endon,cachex,phoenix_html_sanitizer,html_sanitize_ex,
          html_entities,bbmustache,flow,date_time_parser,nimble_csv,
          one_time_pass_ecto,params,password_validator,passgen,xlsxir,
          tailwind,phoenix_multi_select]},
     {description,"mis_reports"},
     {modules,
         ['Elixir.EmployeeBenefitLive.EmployeeBenefitComponent',
          'Elixir.Jason.Encoder.MisReports.Accounts.User',
          'Elixir.Jason.Encoder.MisReports.Audit.SystemException',
          'Elixir.MisReports','Elixir.MisReports.Accounts',
          'Elixir.MisReports.Accounts.User',
          'Elixir.MisReports.Accounts.User.Localtime',
          'Elixir.MisReports.Accounts.User.Password',
          'Elixir.MisReports.Accounts.User.UserPassword',
          'Elixir.MisReports.Accounts.UserRole',
          'Elixir.MisReports.Application','Elixir.MisReports.Audit',
          'Elixir.MisReports.Audit.SystemException',
          'Elixir.MisReports.Audit.UserLog','Elixir.MisReports.Auth',
          'Elixir.MisReports.Controller','Elixir.MisReports.Emails.Email',
          'Elixir.MisReports.Employees',
          'Elixir.MisReports.Employees.EmployeeBenefit',
          'Elixir.MisReports.Employees.EmployeeStats',
          'Elixir.MisReports.Enums.FileTypes',
          'Elixir.MisReports.IncomeStatement','Elixir.MisReports.Mailer',
          'Elixir.MisReports.Mappings',
          'Elixir.MisReports.Mappings.BankAccount',
          'Elixir.MisReports.Mappings.DataFields',
          'Elixir.MisReports.Mappings.FileSpec',
          'Elixir.MisReports.Mappings.FileSpec.Column',
          'Elixir.MisReports.Mappings.FileSpecColumns',
          'Elixir.MisReports.Mappings.GlMapping',
          'Elixir.MisReports.Mappings.ScheduleSectionMapping',
          'Elixir.MisReports.Mappings.TrialBalMapping',
          'Elixir.MisReports.Prudentials',
          'Elixir.MisReports.Prudentials.AccountDomicileBranch',
          'Elixir.MisReports.Prudentials.ArrearsReport',
          'Elixir.MisReports.Prudentials.BsaMaster',
          'Elixir.MisReports.Prudentials.BusinessUnits',
          'Elixir.MisReports.Prudentials.CreditCards',
          'Elixir.MisReports.Prudentials.DaysPastDue',
          'Elixir.MisReports.Prudentials.GdpFile',
          'Elixir.MisReports.Prudentials.GovernmentExposureList',
          'Elixir.MisReports.Prudentials.LargeLoans',
          'Elixir.MisReports.Prudentials.LoanAdvance',
          'Elixir.MisReports.Prudentials.LoanClassifications',
          'Elixir.MisReports.Prudentials.LoanInsiderLending',
          'Elixir.MisReports.Prudentials.LoanProducts',
          'Elixir.MisReports.Prudentials.LoanSchemeCodes',
          'Elixir.MisReports.Prudentials.LoanSectors',
          'Elixir.MisReports.Prudentials.Obddr',
          'Elixir.MisReports.Prudentials.ObddrSectors',
          'Elixir.MisReports.Prudentials.PrudReport',
          'Elixir.MisReports.Prudentials.RegulatoryCapital',
          'Elixir.MisReports.Prudentials.ReliefList','Elixir.MisReports.Repo',
          'Elixir.MisReports.ReportGens.CreditMisReports.SpecificProvisionMovement',
          'Elixir.MisReports.ScheduleLoader','Elixir.MisReports.Scheduler',
          'Elixir.MisReports.SourceData',
          'Elixir.MisReports.SourceData.AllDeal',
          'Elixir.MisReports.SourceData.CmmpBranches',
          'Elixir.MisReports.SourceData.CmmpProduct',
          'Elixir.MisReports.SourceData.CounterpartySecurities',
          'Elixir.MisReports.SourceData.CreditLineAvbl',
          'Elixir.MisReports.SourceData.CustContribution',
          'Elixir.MisReports.SourceData.CustSegment',
          'Elixir.MisReports.SourceData.DailyAllDeal',
          'Elixir.MisReports.SourceData.DailyGut',
          'Elixir.MisReports.SourceData.DebtorsBookAnalysis',
          'Elixir.MisReports.SourceData.FinnacleTb',
          'Elixir.MisReports.SourceData.FxCashFlow',
          'Elixir.MisReports.SourceData.GmoTpins',
          'Elixir.MisReports.SourceData.GovtAccounts',
          'Elixir.MisReports.SourceData.NostroAccounts',
          'Elixir.MisReports.SourceData.QuarterlySrcFiles',
          'Elixir.MisReports.SourceData.RawSchedules',
          'Elixir.MisReports.SourceData.TrialBalScrFile',
          'Elixir.MisReports.SourceData.TrialBalance',
          'Elixir.MisReports.SourceData.WeeklyReportEntriesAllDeal',
          'Elixir.MisReports.SourceData.WeeklyReportEntriesCustContribution',
          'Elixir.MisReports.SourceData.WeeklyReportSrcFile',
          'Elixir.MisReports.Templates',
          'Elixir.MisReports.Templates.DocStorage',
          'Elixir.MisReports.Upload.Service','Elixir.MisReports.Uploads',
          'Elixir.MisReports.Utilities',
          'Elixir.MisReports.Utilities.Adjustments',
          'Elixir.MisReports.Utilities.Allowances',
          'Elixir.MisReports.Utilities.BalanceDueDomestic',
          'Elixir.MisReports.Utilities.Branch',
          'Elixir.MisReports.Utilities.CompanySettings',
          'Elixir.MisReports.Utilities.CompanySettings.SystemSettings',
          'Elixir.MisReports.Utilities.Customer',
          'Elixir.MisReports.Utilities.ExchangePlacement',
          'Elixir.MisReports.Utilities.ExchangeRates',
          'Elixir.MisReports.Utilities.FileExport',
          'Elixir.MisReports.Utilities.GovAccount',
          'Elixir.MisReports.Utilities.Insertion',
          'Elixir.MisReports.Utilities.InstitutionDetails',
          'Elixir.MisReports.Utilities.OffBlcSheet',
          'Elixir.MisReports.Utilities.RepossedProp',
          'Elixir.MisReports.Utilities.SecureHoldings',
          'Elixir.MisReports.Utilities.Securities',
          'Elixir.MisReports.Utilities.ShareHolders',
          'Elixir.MisReports.Utilities.SubmissionDates',
          'Elixir.MisReports.Utilities.SysDirectory',
          'Elixir.MisReports.Utilities.SysDirectory.Directories',
          'Elixir.MisReports.Utilities.Tax',
          'Elixir.MisReports.Utilities.TechnologicalInfrastructure',
          'Elixir.MisReports.Utilities.WeeklyBozBalance',
          'Elixir.MisReports.Workers.BalanceSheet',
          'Elixir.MisReports.Workers.BozReq.BalanceSheet',
          'Elixir.MisReports.Workers.BozReq.IncomeStatement',
          'Elixir.MisReports.Workers.BozReq.Schedule01c',
          'Elixir.MisReports.Workers.BozReq.Schedule01e',
          'Elixir.MisReports.Workers.BozReq.Schedule01f',
          'Elixir.MisReports.Workers.BozReq.Schedule02g',
          'Elixir.MisReports.Workers.BozReq.Schedule03a',
          'Elixir.MisReports.Workers.BozReq.Schedule04b',
          'Elixir.MisReports.Workers.BozReq.Schedule04d',
          'Elixir.MisReports.Workers.BozReq.Schedule11d',
          'Elixir.MisReports.Workers.BozReq.Schedule11e',
          'Elixir.MisReports.Workers.BozReq.Schedule11f',
          'Elixir.MisReports.Workers.BozReq.Schedule11g',
          'Elixir.MisReports.Workers.BozReq.Schedule11j',
          'Elixir.MisReports.Workers.BozReq.Schedule12',
          'Elixir.MisReports.Workers.BozReq.Schedule14',
          'Elixir.MisReports.Workers.BozReq.Schedule15',
          'Elixir.MisReports.Workers.BozReq.Schedule17a',
          'Elixir.MisReports.Workers.BozReq.Schedule17b',
          'Elixir.MisReports.Workers.BozReq.Schedule17e',
          'Elixir.MisReports.Workers.BozReq.Schedule18a',
          'Elixir.MisReports.Workers.BozReq.Schedule18b',
          'Elixir.MisReports.Workers.BozReq.Schedule18c',
          'Elixir.MisReports.Workers.BozReq.Schedule18d',
          'Elixir.MisReports.Workers.BozReq.Schedule19',
          'Elixir.MisReports.Workers.BozReq.Schedule20a',
          'Elixir.MisReports.Workers.BozReq.Schedule21b',
          'Elixir.MisReports.Workers.BozReq.Schedule21c',
          'Elixir.MisReports.Workers.BozReq.Schedule22a',
          'Elixir.MisReports.Workers.BozReq.Schedule24',
          'Elixir.MisReports.Workers.BozReq.Schedule25',
          'Elixir.MisReports.Workers.BozReq.Schedule26',
          'Elixir.MisReports.Workers.BozReq.Schedule27a',
          'Elixir.MisReports.Workers.BozReq.Schedule28a',
          'Elixir.MisReports.Workers.BozReq.Schedule28b',
          'Elixir.MisReports.Workers.BozReq.Schedule29a',
          'Elixir.MisReports.Workers.BozReq.Schedule2a1',
          'Elixir.MisReports.Workers.BozReq.Schedule2h',
          'Elixir.MisReports.Workers.BozReq.Schedule30c',
          'Elixir.MisReports.Workers.BozReq.Schedule30d',
          'Elixir.MisReports.Workers.BozReq.Schedule31c',
          'Elixir.MisReports.Workers.BozReq.Schedule31d',
          'Elixir.MisReports.Workers.BozReq.Schedule31f',
          'Elixir.MisReports.Workers.BozReq.Schedule32a',
          'Elixir.MisReports.Workers.BozReq.Schedule3a',
          'Elixir.MisReports.Workers.BozReq.Schedule4b',
          'Elixir.MisReports.Workers.BozReq.Schedule4d',
          'Elixir.MisReports.Workers.BozReq.Schedule5b',
          'Elixir.MisReports.Workers.BozReq.Schedule6a',
          'Elixir.MisReports.Workers.BozReq.Schedule7a',
          'Elixir.MisReports.Workers.BozReq.Schedule8b',
          'Elixir.MisReports.Workers.BsaExcelWorker',
          'Elixir.MisReports.Workers.BsaReturns',
          'Elixir.MisReports.Workers.CellFormat',
          'Elixir.MisReports.Workers.ExcelWorker',
          'Elixir.MisReports.Workers.Export',
          'Elixir.MisReports.Workers.FileParser',
          'Elixir.MisReports.Workers.IS',
          'Elixir.MisReports.Workers.Jobs.DailyAllDealUpload',
          'Elixir.MisReports.Workers.Jobs.DailyFinnacleTbUpload',
          'Elixir.MisReports.Workers.Jobs.DailyGutUpload',
          'Elixir.MisReports.Workers.Jobs.GenMonthlyPrudential',
          'Elixir.MisReports.Workers.Jobs.InitiateTask',
          'Elixir.MisReports.Workers.Jobs.NostroAccountsUpload',
          'Elixir.MisReports.Workers.Jobs.ProcessTrialBalIS',
          'Elixir.MisReports.Workers.Jobs.ProcessTrialBalUpload',
          'Elixir.MisReports.Workers.Jobs.ProcessUpload',
          'Elixir.MisReports.Workers.Jobs.QuarterlyBranchesUpload',
          'Elixir.MisReports.Workers.Jobs.QuarterlyProductUpload',
          'Elixir.MisReports.Workers.Jobs.SaveMonthyPrudential',
          'Elixir.MisReports.Workers.Jobs.UploadCustDtls',
          'Elixir.MisReports.Workers.Jobs.UploadCustDtlsFinance',
          'Elixir.MisReports.Workers.Jobs.UploadGmoTpins',
          'Elixir.MisReports.Workers.Jobs.WeeklyUploadAllDeal',
          'Elixir.MisReports.Workers.Jobs.WeeklyUploadCustContribution',
          'Elixir.MisReports.Workers.LoansAdvances.FundingSource',
          'Elixir.MisReports.Workers.LoansAdvances.Sh02c',
          'Elixir.MisReports.Workers.LoansAdvances.Sh02d',
          'Elixir.MisReports.Workers.LoansAdvances.Sh02g',
          'Elixir.MisReports.Workers.LoansAdvances.Sh02h',
          'Elixir.MisReports.Workers.LoansAdvances.Sh03a',
          'Elixir.MisReports.Workers.LoansAdvances.Sh14',
          'Elixir.MisReports.Workers.LoansAdvances.Sh17b',
          'Elixir.MisReports.Workers.LoansAdvances.Sh18b',
          'Elixir.MisReports.Workers.LoansAdvances.Sh22a',
          'Elixir.MisReports.Workers.LoansAdvances.Sh22b',
          'Elixir.MisReports.Workers.LoansAdvances.Sh2a1',
          'Elixir.MisReports.Workers.LoansAdvances.Sh4b',
          'Elixir.MisReports.Workers.LoansAdvances.Sh4d',
          'Elixir.MisReports.Workers.LoansAdvances.Sh5b',
          'Elixir.MisReports.Workers.LoansAdvances.Sh5d',
          'Elixir.MisReports.Workers.LoansAdvances.Sh6a',
          'Elixir.MisReports.Workers.LoansAdvances.Sh6b',
          'Elixir.MisReports.Workers.LoansAdvances.Sh7a',
          'Elixir.MisReports.Workers.LoansAdvances.Sh8a',
          'Elixir.MisReports.Workers.LoansAdvances.Sh8b',
          'Elixir.MisReports.Workers.Me1',
          'Elixir.MisReports.Workers.PopulateMonthlyPrudential',
          'Elixir.MisReports.Workers.Quartely.IncomeStatementTrend',
          'Elixir.MisReports.Workers.Quartely.StatementOfLiquidityPosition',
          'Elixir.MisReports.Workers.ReportGens.Cmmp.AgricultureLarge',
          'Elixir.MisReports.Workers.ReportGens.Cmmp.BusinessLarge',
          'Elixir.MisReports.Workers.ReportGens.Cmmp.BusinessSmall',
          'Elixir.MisReports.Workers.ReportGens.Cmmp.Geographical',
          'Elixir.MisReports.Workers.ReportGens.Cmmp.Government',
          'Elixir.MisReports.Workers.ReportGens.Cmmp.HouseholdIndividual',
          'Elixir.MisReports.Workers.ReportGens.Cmmp.Totals',
          'Elixir.MisReports.Workers.ReportGens.CreditMisReports.LoanClassifications',
          'Elixir.MisReports.Workers.ReportGens.LoansAdvances.CreditMisReports.SectorAnalysis',
          'Elixir.MisReports.Workers.ReportGens.Mis.CostAnalysis',
          'Elixir.MisReports.Workers.ReportGens.Mis.ProfitLossAnalysis',
          'Elixir.MisReports.Workers.ReportGens.Weekly.CoreLiquidAssets.Sh21a',
          'Elixir.MisReports.Workers.ReportGens.Weekly.CoreLiquidAssets.Sh21b',
          'Elixir.MisReports.Workers.ReportGens.Weekly.CoreLiquidAssets.Sh27',
          'Elixir.MisReports.Workers.ReportGens.Weekly.CoreLiquidAssets.Sh27a1',
          'Elixir.MisReports.Workers.ReportGens.Weekly.CoreLiquidAssets.Sh27a2',
          'Elixir.MisReports.Workers.ReportGens.Weekly.CoreLiquidAssets.Sh27a3',
          'Elixir.MisReports.Workers.ReportGens.Weekly.CoreLiquidAssets.Sh27a4',
          'Elixir.MisReports.Workers.ReportGens.Weekly.CoreLiquidAssets.Sh27a5',
          'Elixir.MisReports.Workers.ReportGens.Weekly.CoreLiquidAssets.Sh27b1',
          'Elixir.MisReports.Workers.ReportGens.Weekly.CoreLiquidAssets.Sh27b2',
          'Elixir.MisReports.Workers.Sh01c','Elixir.MisReports.Workers.Sh01e',
          'Elixir.MisReports.Workers.Sh01f','Elixir.MisReports.Workers.Sh02g',
          'Elixir.MisReports.Workers.Sh04d','Elixir.MisReports.Workers.Sh09a',
          'Elixir.MisReports.Workers.Sh11d','Elixir.MisReports.Workers.Sh11e',
          'Elixir.MisReports.Workers.Sh11fAndsh11g',
          'Elixir.MisReports.Workers.Sh11j','Elixir.MisReports.Workers.Sh11l',
          'Elixir.MisReports.Workers.Sh12','Elixir.MisReports.Workers.Sh13',
          'Elixir.MisReports.Workers.Sh14','Elixir.MisReports.Workers.Sh15',
          'Elixir.MisReports.Workers.Sh18a','Elixir.MisReports.Workers.Sh18b',
          'Elixir.MisReports.Workers.Sh18c','Elixir.MisReports.Workers.Sh18d',
          'Elixir.MisReports.Workers.Sh19','Elixir.MisReports.Workers.Sh20a',
          'Elixir.MisReports.Workers.Sh21b','Elixir.MisReports.Workers.Sh21c',
          'Elixir.MisReports.Workers.Sh23a','Elixir.MisReports.Workers.Sh23b',
          'Elixir.MisReports.Workers.Sh24','Elixir.MisReports.Workers.Sh25',
          'Elixir.MisReports.Workers.Sh26','Elixir.MisReports.Workers.Sh27',
          'Elixir.MisReports.Workers.Sh27a','Elixir.MisReports.Workers.Sh28a',
          'Elixir.MisReports.Workers.Sh28b','Elixir.MisReports.Workers.Sh29a',
          'Elixir.MisReports.Workers.Sh30b','Elixir.MisReports.Workers.Sh30c',
          'Elixir.MisReports.Workers.Sh30d','Elixir.MisReports.Workers.Sh31c',
          'Elixir.MisReports.Workers.Sh31d','Elixir.MisReports.Workers.Sh31f',
          'Elixir.MisReports.Workers.Sh32a',
          'Elixir.MisReports.Workers.Shd17e',
          'Elixir.MisReports.Workers.Utils',
          'Elixir.MisReports.Workers.Weekly.CLA','Elixir.MisReports.Workflow',
          'Elixir.MisReports.Workflow.Module',
          'Elixir.MisReports.Workflow.Wklaction',
          'Elixir.MisReports.Workflow.WklactionRule',
          'Elixir.MisReports.Workflow.Wklcomment',
          'Elixir.MisReports.Workflow.Wklerror',
          'Elixir.MisReports.Workflow.Wklprocess',
          'Elixir.MisReports.Workflow.WklreportPeriods',
          'Elixir.MisReports.Workflow.Wklrequest',
          'Elixir.MisReports.Workflow.WklspecialRule',
          'Elixir.MisReports.Workflow.Wklstep',
          'Elixir.MisReports.Workflow.WklstepRule',
          'Elixir.MisReports.Workflow.WkltaskAttachment',
          'Elixir.MisReports.Workflow.WkltaskDetail',
          'Elixir.MisReports.Workflow.WkltaskDetailHistory',
          'Elixir.MisReports.Workflow.WkltaskHeader',
          'Elixir.MisReports.Workflow.WkltaskHeaderHistory',
          'Elixir.MisReportsWeb',
          'Elixir.MisReportsWeb.AdjustmentsLive.AdjustmentsComponent',
          'Elixir.MisReportsWeb.AdjustmentsLive.Index',
          'Elixir.MisReportsWeb.AdjustmentsView',
          'Elixir.MisReportsWeb.AllowancesLive.AllowancesComponent',
          'Elixir.MisReportsWeb.AllowancesLive.Index',
          'Elixir.MisReportsWeb.AllowancesView',
          'Elixir.MisReportsWeb.BalanceDueDomesticLive.BalanceDueDomesticComponent',
          'Elixir.MisReportsWeb.BalanceDueDomesticLive.Index',
          'Elixir.MisReportsWeb.BalanceDueDomesticView',
          'Elixir.MisReportsWeb.BalanceSheetLive.Index',
          'Elixir.MisReportsWeb.BalanceSheetView',
          'Elixir.MisReportsWeb.BalancesDueBanksLIve.BalancesDueBanksComponent',
          'Elixir.MisReportsWeb.BalancesDueBanksLive.Index',
          'Elixir.MisReportsWeb.BalancesDueBanksView',
          'Elixir.MisReportsWeb.BranchController',
          'Elixir.MisReportsWeb.BranchLive.BranchComponent',
          'Elixir.MisReportsWeb.BranchLive.Index',
          'Elixir.MisReportsWeb.BranchView',
          'Elixir.MisReportsWeb.BsDashboardView',
          'Elixir.MisReportsWeb.BsaReportsLive.Index',
          'Elixir.MisReportsWeb.BsaReportsView',
          'Elixir.MisReportsWeb.BusinessUnitLive.BusinessUnitComponent',
          'Elixir.MisReportsWeb.BusinessUnitLive.Index',
          'Elixir.MisReportsWeb.BusinessUnitView',
          'Elixir.MisReportsWeb.CmmpLIve.CmmpComponent',
          'Elixir.MisReportsWeb.CmmpLive.Index',
          'Elixir.MisReportsWeb.CmmpProductLive.CmmpProductComponent',
          'Elixir.MisReportsWeb.CmmpProductLive.Index',
          'Elixir.MisReportsWeb.CmmpProductView',
          'Elixir.MisReportsWeb.CmmpView','Elixir.MisReportsWeb.Components',
          'Elixir.MisReportsWeb.Components.CommentModalComponent',
          'Elixir.MisReportsWeb.Components.Shared.NavigationMenu',
          'Elixir.MisReportsWeb.Components.Shared.WeeklyNavigationMenu',
          'Elixir.MisReportsWeb.Components.TabbedAdjustments',
          'Elixir.MisReportsWeb.Components.TabbedReports',
          'Elixir.MisReportsWeb.Components.TabsForAdj',
          'Elixir.MisReportsWeb.Components.TaskComponents',
          'Elixir.MisReportsWeb.Components.TasksComponents',
          'Elixir.MisReportsWeb.CoreComponents',
          'Elixir.MisReportsWeb.CostAnalysisLive.CostAnalysisComponent',
          'Elixir.MisReportsWeb.CostAnalysisLive.Index',
          'Elixir.MisReportsWeb.CostAnalysisView',
          'Elixir.MisReportsWeb.CounterpartySecuritiesLive.CounterpartySecuritiesComponent',
          'Elixir.MisReportsWeb.CounterpartySecuritiesLive.Index',
          'Elixir.MisReportsWeb.CounterpartySecuritiesView',
          'Elixir.MisReportsWeb.CreditLive.Index',
          'Elixir.MisReportsWeb.CreditView',
          'Elixir.MisReportsWeb.CurrentDefferredLive.CurrentDefferredComponent',
          'Elixir.MisReportsWeb.CurrentDefferredLive.Index',
          'Elixir.MisReportsWeb.CurrentTaxView',
          'Elixir.MisReportsWeb.DashboardLive.Index',
          'Elixir.MisReportsWeb.DataTable',
          'Elixir.MisReportsWeb.DebtorsBookAnalysisLive.DebtorsBookAnalysisComponent',
          'Elixir.MisReportsWeb.DebtorsBookAnalysisLive.Index',
          'Elixir.MisReportsWeb.DebtorsBookAnalysisView',
          'Elixir.MisReportsWeb.DepositLIve.DepositComponent',
          'Elixir.MisReportsWeb.DepositLive.Index',
          'Elixir.MisReportsWeb.DepositView',
          'Elixir.MisReportsWeb.DocStorageController',
          'Elixir.MisReportsWeb.EmailView',
          'Elixir.MisReportsWeb.EmployeeBenefitLive.Index',
          'Elixir.MisReportsWeb.EmployeeBenefitView',
          'Elixir.MisReportsWeb.EmployeeStatsLive.EmployeeStatsComponent',
          'Elixir.MisReportsWeb.EmployeeStatsLive.Index',
          'Elixir.MisReportsWeb.EmployeeStatsView',
          'Elixir.MisReportsWeb.Endpoint','Elixir.MisReportsWeb.ErrorHelpers',
          'Elixir.MisReportsWeb.ErrorView',
          'Elixir.MisReportsWeb.ExchangePlacementLive.ExchangePlacementComponent',
          'Elixir.MisReportsWeb.ExchangePlacementLive.Index',
          'Elixir.MisReportsWeb.ExchangePlacementView',
          'Elixir.MisReportsWeb.ExchangeRateLive.ExchangeRateComponent',
          'Elixir.MisReportsWeb.ExchangeRateLive.Index',
          'Elixir.MisReportsWeb.ExchangeRateView',
          'Elixir.MisReportsWeb.FinanceLive.Index',
          'Elixir.MisReportsWeb.FinanceView',
          'Elixir.MisReportsWeb.FlashMessage','Elixir.MisReportsWeb.Gettext',
          'Elixir.MisReportsWeb.GovtAccountsLive.GovtAccountsComponent',
          'Elixir.MisReportsWeb.GovtAccountsLive.Index',
          'Elixir.MisReportsWeb.GovtAccountsView',
          'Elixir.MisReportsWeb.IncomeStatementLive.IncomeStatementComponent',
          'Elixir.MisReportsWeb.IncomeStatementLive.Index',
          'Elixir.MisReportsWeb.IncomeStatementTrendLive.Index',
          'Elixir.MisReportsWeb.IncomeStatementTrendView',
          'Elixir.MisReportsWeb.IncomeStatementView',
          'Elixir.MisReportsWeb.InframeController',
          'Elixir.MisReportsWeb.InputHelpers',
          'Elixir.MisReportsWeb.InsertionLive.Index',
          'Elixir.MisReportsWeb.InsertionLive.InsertionComponent',
          'Elixir.MisReportsWeb.InsertionView',
          'Elixir.MisReportsWeb.InstitutionDetailsLive.Index',
          'Elixir.MisReportsWeb.InstitutionDetailsLive.InstitutionDetailsComponent',
          'Elixir.MisReportsWeb.InstitutionDetailsView',
          'Elixir.MisReportsWeb.IsDashboardComponent',
          'Elixir.MisReportsWeb.IsDashboardLive.Index',
          'Elixir.MisReportsWeb.IsDashboardView',
          'Elixir.MisReportsWeb.LayoutView',
          'Elixir.MisReportsWeb.LiveHelpers',
          'Elixir.MisReportsWeb.LoaderView',
          'Elixir.MisReportsWeb.LoanAdvancesLIve.LoanAdvancesComponent',
          'Elixir.MisReportsWeb.LoanAdvancesLIve.LoanBanksComponent',
          'Elixir.MisReportsWeb.LoanAdvancesLive.Index',
          'Elixir.MisReportsWeb.LoanAdvancesView',
          'Elixir.MisReportsWeb.LoanBanksLive.Index',
          'Elixir.MisReportsWeb.LoanBanksView',
          'Elixir.MisReportsWeb.LoanBusinessUnitView',
          'Elixir.MisReportsWeb.LoanClassificationLive.Index',
          'Elixir.MisReportsWeb.LoanClassificationLive.LoanClassificationComponent',
          'Elixir.MisReportsWeb.LoanClassificationView',
          'Elixir.MisReportsWeb.LoanProductLive.Index',
          'Elixir.MisReportsWeb.LoanProductLive.LoanProductComponent',
          'Elixir.MisReportsWeb.LoanProductView',
          'Elixir.MisReportsWeb.LoanSchemeCodeLive.Index',
          'Elixir.MisReportsWeb.LoanSchemeCodeLive.LoanSchemeCodeComponent',
          'Elixir.MisReportsWeb.LoanSectorLive.Index',
          'Elixir.MisReportsWeb.LoanSectorLive.LoanSectorComponent',
          'Elixir.MisReportsWeb.LoanSectorView',
          'Elixir.MisReportsWeb.MappingLive.AccListComponent',
          'Elixir.MisReportsWeb.MappingLive.BankAccComponent',
          'Elixir.MisReportsWeb.MappingLive.FileSpecComponent',
          'Elixir.MisReportsWeb.MappingLive.GlMappingComponent',
          'Elixir.MisReportsWeb.MappingLive.Index',
          'Elixir.MisReportsWeb.MappingLive.MenuComponent',
          'Elixir.MisReportsWeb.MappingView',
          'Elixir.MisReportsWeb.MisReportLive.Index',
          'Elixir.MisReportsWeb.MisReportLive.MisReportComponent',
          'Elixir.MisReportsWeb.MisReportView','Elixir.MisReportsWeb.Modals',
          'Elixir.MisReportsWeb.Notifications',
          'Elixir.MisReportsWeb.OffBlcSheetLive.Index',
          'Elixir.MisReportsWeb.OffBlcSheetLive.OffBlcSheetComponent',
          'Elixir.MisReportsWeb.OffBlcSheetView',
          'Elixir.MisReportsWeb.OtherSchedulesLive.Index',
          'Elixir.MisReportsWeb.OtherSchedulesView',
          'Elixir.MisReportsWeb.PageController',
          'Elixir.MisReportsWeb.PageSection','Elixir.MisReportsWeb.PageView',
          'Elixir.MisReportsWeb.Plugs.Authenticate',
          'Elixir.MisReportsWeb.Plugs.EnforcePasswordPolicy',
          'Elixir.MisReportsWeb.Plugs.RequireAuth',
          'Elixir.MisReportsWeb.Plugs.SessionTimeout',
          'Elixir.MisReportsWeb.Plugs.SetUser',
          'Elixir.MisReportsWeb.PropertiesView',
          'Elixir.MisReportsWeb.PrudentialController',
          'Elixir.MisReportsWeb.PrudentialReportLive.Index',
          'Elixir.MisReportsWeb.PrudentialReportView',
          'Elixir.MisReportsWeb.QuarterlyLive.Index',
          'Elixir.MisReportsWeb.QuarterlyLive.QuarterlyComponent',
          'Elixir.MisReportsWeb.QuarterlyView',
          'Elixir.MisReportsWeb.ReckonLive.Index',
          'Elixir.MisReportsWeb.ReckonLive.ReckonComponent',
          'Elixir.MisReportsWeb.ReckonView',
          'Elixir.MisReportsWeb.RegulatoryCapitalLive.Index',
          'Elixir.MisReportsWeb.RegulatoryCapitalLive.RegulatoryCapitalComponent',
          'Elixir.MisReportsWeb.RegulatoryCapitalView',
          'Elixir.MisReportsWeb.ReportsController',
          'Elixir.MisReportsWeb.ReportsLive.Index',
          'Elixir.MisReportsWeb.ReportsView',
          'Elixir.MisReportsWeb.RepossessedPropertiesController',
          'Elixir.MisReportsWeb.RepossessedPropertiesLive.Index',
          'Elixir.MisReportsWeb.RepossessedPropertiesLive.RepossessedPropertiesComponent',
          'Elixir.MisReportsWeb.RepossessedPropertiesView',
          'Elixir.MisReportsWeb.ReturnsController',
          'Elixir.MisReportsWeb.ReturnsView','Elixir.MisReportsWeb.Router',
          'Elixir.MisReportsWeb.Router.Helpers',
          'Elixir.MisReportsWeb.ScheduleLIve.SchedulesComponent',
          'Elixir.MisReportsWeb.SchedulesLive.Index',
          'Elixir.MisReportsWeb.SchedulesView',
          'Elixir.MisReportsWeb.SchemeCodeView',
          'Elixir.MisReportsWeb.SecureHoldingsLive.Index',
          'Elixir.MisReportsWeb.SecureHoldingsLive.SecureHoldingsComponent',
          'Elixir.MisReportsWeb.SecureHoldingsView',
          'Elixir.MisReportsWeb.SecuritiesController',
          'Elixir.MisReportsWeb.SecuritiesLive.Index',
          'Elixir.MisReportsWeb.SecuritiesLive.SecuritiesComponent',
          'Elixir.MisReportsWeb.SecuritiesView',
          'Elixir.MisReportsWeb.SessionController',
          'Elixir.MisReportsWeb.SessionView',
          'Elixir.MisReportsWeb.SettingsLive.CompanySettingsComponent',
          'Elixir.MisReportsWeb.SettingsLive.DirectoriesComponent',
          'Elixir.MisReportsWeb.SettingsLive.GovAccountsComponent',
          'Elixir.MisReportsWeb.SettingsLive.Index',
          'Elixir.MisReportsWeb.SettingsView',
          'Elixir.MisReportsWeb.ShareHoldersLive.Index',
          'Elixir.MisReportsWeb.ShareHoldersLive.ShareHoldersComponent',
          'Elixir.MisReportsWeb.ShareHoldersView',
          'Elixir.MisReportsWeb.SortIcon',
          'Elixir.MisReportsWeb.SourceDataLive.CrrComponent',
          'Elixir.MisReportsWeb.SourceDataLive.Index',
          'Elixir.MisReportsWeb.SourceDataLive.SegementationComponent',
          'Elixir.MisReportsWeb.SourceDataLive.TemplateListComponent',
          'Elixir.MisReportsWeb.SourceDataLive.UploadTemplate',
          'Elixir.MisReportsWeb.SourceDataView',
          'Elixir.MisReportsWeb.SubmissionDatesLive.Index',
          'Elixir.MisReportsWeb.SubmissionDatesLive.SubmissionDatesComponent',
          'Elixir.MisReportsWeb.SubmissionDatesView',
          'Elixir.MisReportsWeb.TechnologicalInfrastructureLive.Index',
          'Elixir.MisReportsWeb.TechnologicalInfrastructureView',
          'Elixir.MisReportsWeb.Telemetry',
          'Elixir.MisReportsWeb.TemplateSampleView',
          'Elixir.MisReportsWeb.UploadsController',
          'Elixir.MisReportsWeb.UserChannel',
          'Elixir.MisReportsWeb.UserController',
          'Elixir.MisReportsWeb.UserLive.Index',
          'Elixir.MisReportsWeb.UserLive.MenuComponent',
          'Elixir.MisReportsWeb.UserLive.PasswordComponent',
          'Elixir.MisReportsWeb.UserLive.RoleComponent',
          'Elixir.MisReportsWeb.UserLive.UserComponent',
          'Elixir.MisReportsWeb.UserLiveAuth',
          'Elixir.MisReportsWeb.UserRoleController',
          'Elixir.MisReportsWeb.UserRoleLive.Index',
          'Elixir.MisReportsWeb.UserRoleLive.RoleComponent',
          'Elixir.MisReportsWeb.UserRoleView',
          'Elixir.MisReportsWeb.UserSocket','Elixir.MisReportsWeb.UserView',
          'Elixir.MisReportsWeb.ViewExchangeRateView',
          'Elixir.MisReportsWeb.WeeklyBozBalanceLive.Index',
          'Elixir.MisReportsWeb.WeeklyBozBalanceLive.WeeklyBozBalanceComponent',
          'Elixir.MisReportsWeb.WeeklyBozBalanceView',
          'Elixir.MisReportsWeb.WeeklyLive.Index',
          'Elixir.MisReportsWeb.WeeklyLive.WeeklyComponent',
          'Elixir.MisReportsWeb.WeeklyView',
          'Elixir.MisReportsWeb.WklActionLive.Index',
          'Elixir.MisReportsWeb.WklActionLive.WklActionComponent',
          'Elixir.MisReportsWeb.WklActionRuleLive.Index',
          'Elixir.MisReportsWeb.WklActionRuleLive.WklActionRuleComponent',
          'Elixir.MisReportsWeb.WklActionView',
          'Elixir.MisReportsWeb.WklPendingTasksLive.Index',
          'Elixir.MisReportsWeb.WklPendingTasksLive.WklPendingTasksComponent',
          'Elixir.MisReportsWeb.WklPendingTasksView',
          'Elixir.MisReportsWeb.WklProcessLive.Index',
          'Elixir.MisReportsWeb.WklProcessLive.WklProcessComponent',
          'Elixir.MisReportsWeb.WklProcessView',
          'Elixir.MisReportsWeb.WklStepActionMapView',
          'Elixir.MisReportsWeb.WklStepLive.Index',
          'Elixir.MisReportsWeb.WklStepLive.WklStepComponent',
          'Elixir.MisReportsWeb.WklStepView',
          'Elixir.MisReportsWeb.WklTasksLive.HistoryComponent',
          'Elixir.MisReportsWeb.WklTasksLive.Index',
          'Elixir.MisReportsWeb.WklTasksLive.WklTasksComponent',
          'Elixir.MisReportsWeb.WklTasksView',
          'Elixir.PbsSmsWeb.LoadingComponent',
          'Elixir.TechnologicalInfrastructureLive.TechnologicalInfrastructureComponent']},
     {registered,[]},
     {vsn,"0.1.0"},
     {mod,{'Elixir.MisReports.Application',[]}}]}.
