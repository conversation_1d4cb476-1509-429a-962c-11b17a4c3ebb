defmodule MisReports.Repo.Migrations.TblBozSchedulesResponse do
  use Ecto.Migration

  def change do
    #  create table(:tbl_boz_schedules_response) do
    #   add :schedule, :string
    #   add :filename, :string
    #   add :response, :string
    #   add :finyear, :integer
    #   add :start_date, :date
    #   add :end_date, :date
    #   add :status, :string

    #   timestamps()
    # end
  end
end
