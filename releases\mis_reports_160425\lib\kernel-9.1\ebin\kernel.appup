%% -*- erlang -*-
%% %CopyrightBegin%
%%
%% Copyright Ericsson AB 1999-2021. All Rights Reserved.
%%
%% Licensed under the Apache License, Version 2.0 (the "License");
%% you may not use this file except in compliance with the License.
%% You may obtain a copy of the License at
%%
%%     http://www.apache.org/licenses/LICENSE-2.0
%%
%% Unless required by applicable law or agreed to in writing, software
%% distributed under the License is distributed on an "AS IS" BASIS,
%% WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
%% See the License for the specific language governing permissions and
%% limitations under the License.
%%
%% %CopyrightEnd%
%%
%% We allow upgrade from, and downgrade to all previous
%% versions from the following OTP releases:
%% - OTP 24
%% - OTP 25
%% - OTP 26
%%
%% We also allow upgrade from, and downgrade to all
%% versions that have branched off from the above
%% stated previous versions.
%%
{"9.1",
 [{<<"^8\\.0$">>,[restart_new_emulator]},
  {<<"^8\\.0\\.0(?:\\.[0-9]+)+$">>,[restart_new_emulator]},
  {<<"^8\\.0\\.1(?:\\.[0-9]+)*$">>,[restart_new_emulator]},
  {<<"^8\\.0\\.2(?:\\.[0-9]+)*$">>,[restart_new_emulator]},
  {<<"^8\\.1$">>,[restart_new_emulator]},
  {<<"^8\\.1\\.0(?:\\.[0-9]+)+$">>,[restart_new_emulator]},
  {<<"^8\\.1\\.1(?:\\.[0-9]+)*$">>,[restart_new_emulator]},
  {<<"^8\\.1\\.2(?:\\.[0-9]+)*$">>,[restart_new_emulator]},
  {<<"^8\\.1\\.3(?:\\.[0-9]+)*$">>,[restart_new_emulator]},
  {<<"^8\\.2$">>,[restart_new_emulator]},
  {<<"^8\\.2\\.0(?:\\.[0-9]+)+$">>,[restart_new_emulator]},
  {<<"^8\\.3$">>,[restart_new_emulator]},
  {<<"^8\\.3\\.0(?:\\.[0-9]+)+$">>,[restart_new_emulator]},
  {<<"^8\\.3\\.1(?:\\.[0-9]+)*$">>,[restart_new_emulator]},
  {<<"^8\\.3\\.2(?:\\.[0-9]+)*$">>,[restart_new_emulator]},
  {<<"^8\\.4$">>,[restart_new_emulator]},
  {<<"^8\\.4\\.0(?:\\.[0-9]+)+$">>,[restart_new_emulator]},
  {<<"^8\\.4\\.1(?:\\.[0-9]+)*$">>,[restart_new_emulator]},
  {<<"^8\\.4\\.2(?:\\.[0-9]+)*$">>,[restart_new_emulator]},
  {<<"^8\\.5$">>,[restart_new_emulator]},
  {<<"^8\\.5\\.0(?:\\.[0-9]+)+$">>,[restart_new_emulator]},
  {<<"^8\\.5\\.1(?:\\.[0-9]+)*$">>,[restart_new_emulator]},
  {<<"^8\\.5\\.2(?:\\.[0-9]+)*$">>,[restart_new_emulator]},
  {<<"^8\\.5\\.3(?:\\.[0-9]+)*$">>,[restart_new_emulator]},
  {<<"^8\\.5\\.4(?:\\.[0-9]+)*$">>,[restart_new_emulator]},
  {<<"^9\\.0$">>,[restart_new_emulator]},
  {<<"^9\\.0\\.0(?:\\.[0-9]+)+$">>,[restart_new_emulator]},
  {<<"^9\\.0\\.1(?:\\.[0-9]+)*$">>,[restart_new_emulator]},
  {<<"^9\\.0\\.2(?:\\.[0-9]+)*$">>,[restart_new_emulator]}],
 [{<<"^8\\.0$">>,[restart_new_emulator]},
  {<<"^8\\.0\\.0(?:\\.[0-9]+)+$">>,[restart_new_emulator]},
  {<<"^8\\.0\\.1(?:\\.[0-9]+)*$">>,[restart_new_emulator]},
  {<<"^8\\.0\\.2(?:\\.[0-9]+)*$">>,[restart_new_emulator]},
  {<<"^8\\.1$">>,[restart_new_emulator]},
  {<<"^8\\.1\\.0(?:\\.[0-9]+)+$">>,[restart_new_emulator]},
  {<<"^8\\.1\\.1(?:\\.[0-9]+)*$">>,[restart_new_emulator]},
  {<<"^8\\.1\\.2(?:\\.[0-9]+)*$">>,[restart_new_emulator]},
  {<<"^8\\.1\\.3(?:\\.[0-9]+)*$">>,[restart_new_emulator]},
  {<<"^8\\.2$">>,[restart_new_emulator]},
  {<<"^8\\.2\\.0(?:\\.[0-9]+)+$">>,[restart_new_emulator]},
  {<<"^8\\.3$">>,[restart_new_emulator]},
  {<<"^8\\.3\\.0(?:\\.[0-9]+)+$">>,[restart_new_emulator]},
  {<<"^8\\.3\\.1(?:\\.[0-9]+)*$">>,[restart_new_emulator]},
  {<<"^8\\.3\\.2(?:\\.[0-9]+)*$">>,[restart_new_emulator]},
  {<<"^8\\.4$">>,[restart_new_emulator]},
  {<<"^8\\.4\\.0(?:\\.[0-9]+)+$">>,[restart_new_emulator]},
  {<<"^8\\.4\\.1(?:\\.[0-9]+)*$">>,[restart_new_emulator]},
  {<<"^8\\.4\\.2(?:\\.[0-9]+)*$">>,[restart_new_emulator]},
  {<<"^8\\.5$">>,[restart_new_emulator]},
  {<<"^8\\.5\\.0(?:\\.[0-9]+)+$">>,[restart_new_emulator]},
  {<<"^8\\.5\\.1(?:\\.[0-9]+)*$">>,[restart_new_emulator]},
  {<<"^8\\.5\\.2(?:\\.[0-9]+)*$">>,[restart_new_emulator]},
  {<<"^8\\.5\\.3(?:\\.[0-9]+)*$">>,[restart_new_emulator]},
  {<<"^8\\.5\\.4(?:\\.[0-9]+)*$">>,[restart_new_emulator]},
  {<<"^9\\.0$">>,[restart_new_emulator]},
  {<<"^9\\.0\\.0(?:\\.[0-9]+)+$">>,[restart_new_emulator]},
  {<<"^9\\.0\\.1(?:\\.[0-9]+)*$">>,[restart_new_emulator]},
  {<<"^9\\.0\\.2(?:\\.[0-9]+)*$">>,[restart_new_emulator]}]}.
