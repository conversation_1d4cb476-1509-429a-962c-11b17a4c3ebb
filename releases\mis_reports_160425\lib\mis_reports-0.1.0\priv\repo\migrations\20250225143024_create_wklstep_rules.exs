defmodule MisReports.Repo.Migrations.CreateWklstepRules do
  use Ecto.Migration

  def change do
    create table(:wklstep_rules) do
      add :step_rule_id, :integer
      add :process_id, :integer
      add :step_id, :integer
      add :role, :string
      add :req_map, :string
      add :created_by, :string
      add :created_date, :date
      add :modified_by, :string
      add :modified_date, :date
      add :step_name, :string
      add :status, :string
      add :description, :string
      add :role_id, :integer

      timestamps()
    end
  end
end
