{application,ex_phone_number,
             [{compile_env,[{ex_phone_number,[log_level],error}]},
              {applications,[kernel,stdlib,elixir,logger,sweet_xml]},
              {description,"A library for parsing, formatting, and validating international phone numbers. Based on Google's libphonenumber.\n"},
              {modules,['Elixir.ExPhoneNumber',
                        'Elixir.ExPhoneNumber.Constants.CountryCodeSource',
                        'Elixir.ExPhoneNumber.Constants.ErrorMessages',
                        'Elixir.ExPhoneNumber.Constants.Mappings',
                        'Elixir.ExPhoneNumber.Constants.MatchTypes',
                        'Elixir.ExPhoneNumber.Constants.Patterns',
                        'Elixir.ExPhoneNumber.Constants.PhoneNumberFormats',
                        'Elixir.ExPhoneNumber.Constants.PhoneNumberTypes',
                        'Elixir.ExPhoneNumber.Constants.ValidationResults',
                        'Elixir.ExPhoneNumber.Constants.Values',
                        'Elixir.ExPhoneNumber.Extraction',
                        'Elixir.ExPhoneNumber.Formatting',
                        'Elixir.ExPhoneNumber.Metadata',
                        'Elixir.ExPhoneNumber.Metadata.NumberFormat',
                        'Elixir.ExPhoneNumber.Metadata.PhoneMetadata',
                        'Elixir.ExPhoneNumber.Metadata.PhoneNumberDescription',
                        'Elixir.ExPhoneNumber.Model.PhoneNumber',
                        'Elixir.ExPhoneNumber.Normalization',
                        'Elixir.ExPhoneNumber.Parsing',
                        'Elixir.ExPhoneNumber.Utilities',
                        'Elixir.ExPhoneNumber.Validation']},
              {registered,[]},
              {vsn,"0.4.5"}]}.
