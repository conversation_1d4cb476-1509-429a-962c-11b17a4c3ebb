{application,html_sanitize_ex,
             [{applications,[kernel,stdlib,elixir,logger,mochiweb]},
              {description,"HTML sanitizer for Elixir"},
              {modules,['Elixir.HtmlSanitizeEx',
                        'Elixir.HtmlSanitizeEx.Parser',
                        'Elixir.HtmlSanitizeEx.Scrubber',
                        'Elixir.HtmlSanitizeEx.Scrubber.BasicHTML',
                        'Elixir.HtmlSanitizeEx.Scrubber.CSS',
                        'Elixir.HtmlSanitizeEx.Scrubber.HTML5',
                        'Elixir.HtmlSanitizeEx.Scrubber.MarkdownHTML',
                        'Elixir.HtmlSanitizeEx.Scrubber.Meta',
                        'Elixir.HtmlSanitizeEx.Scrubber.NoScrub',
                        'Elixir.HtmlSanitizeEx.Scrubber.StripTags',
                        'Elixir.HtmlSanitizeEx.Traverser']},
              {registered,[]},
              {vsn,"1.4.3"}]}.
