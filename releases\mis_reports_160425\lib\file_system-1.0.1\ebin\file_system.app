{application,file_system,
             [{applications,[kernel,stdlib,elixir,logger]},
              {description,"An Elixir file system change watcher wrapper based on FS, the native file\nsystem listener.\n"},
              {modules,['Elixir.FileSystem','Elixir.FileSystem.Backend',
                        'Elixir.FileSystem.Backends.FSInotify',
                        'Elixir.FileSystem.Backends.FSMac',
                        'Elixir.FileSystem.Backends.FSPoll',
                        'Elixir.FileSystem.Backends.FSWindows',
                        'Elixir.FileSystem.Worker']},
              {registered,[]},
              {vsn,"1.0.1"}]}.
