defmodule MisReports.Repo.Migrations.CreateTblTechnologicalInfrastructure do
  use Ecto.Migration

  def change do
    create table(:tbl_technological_infrastructure) do
      add :lusaka, :string
      add :central, :string
      add :copperbelt, :string
      add :eastern, :string
      add :southern, :string
      add :luapula, :string
      add :western, :string
      add :northern, :string
      add :north_western, :string
      add :muchinga, :string
      add :status, :string
      add :maker_date, :naive_datetime
      add :checker_date, :naive_datetime
      add :maker_id, references(:tbl_users, on_delete: :nothing)
      add :checker_id, references(:tbl_users, on_delete: :nothing)

      timestamps()
    end

    create index(:tbl_technological_infrastructure, [:maker_id])
    create index(:tbl_technological_infrastructure, [:checker_id])
  end
end
