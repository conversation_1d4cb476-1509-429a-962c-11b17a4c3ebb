
# This file is responsible for configuring your application
# and its dependencies with the aid of the Config module.
#
# This configuration file is loaded before any dependency and
# is restricted to this project.

# General application configuration
import Config

config :mis_reports,
  ecto_repos: [MisReports.Repo]

config :endon,
  repo: MisReports.Repo

# Configures the endpoint
config :mis_reports, MisReportsWeb.Endpoint,
  url: [host: "*************"],
  render_errors: [view: MisReportsWeb.ErrorView, accepts: ~w(html json), layout: false],
  pubsub_server: MisReports.PubSub,
  live_view: [signing_salt: "px/PlWXk"]

config :mis_reports,
  file_storage: [
#    base_path: "D:",
#    templates_path: "FILES/TEMPLATES"
    base_path: "/application",  # Linux path for production
    templates_path: "files/templates"
    # base_path: "/mnt/d",  # Linux path for local machine
    # templates_path: "FILES/TEMPLATES"
  ]

config :mis_reports,
  session_timeout: 3_600,
  smtp_username: "<EMAIL>",
  exchange_rate: "21.0250",
  exposure: "0.015",
  last_month: %{
    retained_earnings: 0,
    fair_value_reserve: 0,
    revaluation_reserves: 0,
    statutory_reserves: 0,
    other_reserves: 0
  }

# Configures the mailer
config :mis_reports, MisReports.Mailer,
  adapter: Bamboo.SMTPAdapter,
  server: "************",
  port: 25,
  username: "",
  password: "",
  tls: :if_available,
  no_mx_lookups: false,
  allowed_tls_versions: [:tlsv1, :"tlsv1.1", :"tlsv1.2"],
  ssl: false,
  auth: :never,
  retries: 2

# Configures the Scheduler
  config :mis_reports, MisReports.Scheduler,
  overlap: false,
  timeout: 7_200_000,
  timezone: "Africa/Cairo",
  jobs: [
    # file_uploads: [
    #   schedule: {:extended, "*/10"},
    #   # schedule: "@weekly",
    #   task: {MisReports.Workers.Jobs.ProcessUpload, :perform, []}
    # ],
    # weekly_report_gen: [
    #   schedule: {:extended, "*/5"},
    #   task: {MisReports.Workers.Jobs.GenWeeklyReport, :perform, []}
    # ],
    # weekly_report_save: [
    #   schedule: {:extended, "*/5"},
    #   task: {MisReports.Workers.Jobs.SaveWeeklyReport, :perform, []}
    # ],
    # save_monthly_prudential: [
    #   schedule: {:extended, "*/60"},
    #   # schedule: "@weekly",
    #   task: {MisReports.Workers.Jobs.SaveMonthyPrudential, :perform, []}
    # ],
    # populate_monthly_prudential: [
    #   schedule: {:extended, "*/55"},
    #   # schedule: "@weekly",
    #   task: {MisReports.Workers.Jobs.GenMonthlyPrudential, :perform, []}
    # ],

    # gen_quarterly_publication: [
    #   schedule: {:extended, "*/55"},
    #   # schedule: "@weekly",
    #   task: {MisReports.Workers.Jobs.GenQuarterlyPublication, :perform, []}
    # ],

    # save_quarterly_publication: [
    #   schedule: {:extended, "*/55"},
    #   # schedule: "@weekly",
    #   task: {MisReports.Workers.Jobs.SaveQuarterlyPublication, :perform, []}
    # ],

    # save_quarterly_cmmp: [
    #   schedule: {:extended, "*/60"},
    #   # schedule: "@weekly",
    #   task: {MisReports.Workers.Jobs.Cmmp, :perform, []}
    # ],

    # populate_quarterly_cmmp: [
    #     schedule: {:extended, "*/55"},
    #     # schedule: "@weekly",
    #     task: {MisReports.Workers.Jobs.GenQuarterlyCmmp, :perform,[]}
    # ]


  ]

# Configure esbuild (the version is required)
config :esbuild,
  version: "0.14.29",
  default: [
    args:
      ~w(js/app.js --bundle --target=es2017 --outdir=../priv/static/assets --external:/fonts/* --external:/images/*),
    cd: Path.expand("../assets", __DIR__),
    env: %{"NODE_PATH" => Path.expand("../deps", __DIR__)}
  ]

# Configures Elixir's Logger
config :logger, :console,
  format: "$time $metadata[$level] $message\n",
  metadata: [:request_id]

# Application logs
config :logger, :console,
  format: "[$level] $message\n",
  colors: [enabled: true]

config :logger,
  backends: [:console, {LoggerFileBackend, :info}, {LoggerFileBackend, :error}],
  format: "[$level] $message\n"

log_file_path =
  "/application/logs/#{Calendar.strftime(Date.utc_today(), "%Y/%b/%x")}"

config :logger, :info,
  path: "#{log_file_path}/info.log",
  level: :info

# config :logger, :error,
#   path: "#{log_file_path}/error.log",
#   level: :error

# Use Jason for JSON parsing in Phoenix
config :phoenix, :json_library, Jason

# tailwind config
config :tailwind,
  version: "3.3.1",
  default: [
    args: ~w(
      --config=tailwind.config.js
      --input=css/app.css
      --output=../priv/static/assets/app.css
    ),
    cd: Path.expand("../assets", __DIR__)
  ]

# Import environment specific config. This must remain at the bottom
# of this file so it overrides the configuration defined above.
import_config "#{config_env()}.exs"

# cmd /K "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" amd64
# . $HOME/.asdf/asdf.sh
# . $HOME/.asdf/completions/asdf.bash

#  check WSL(ubuntu subsystem) host ip from windows ===> wsl hostname -I
# check windows host ip from wsl ===> cat /etc/resolv.conf
