defmodule MisReports.Repo.Migrations.AddTypeToTblInsertionAdj do
  use Ecto.Migration
  def up do
    alter table(:tbl_insertion_adj) do
      add :type, :string
      add :schedule, :map
      remove :schedule_2c
      remove :schedule_2g
      remove :schedule_3a
      remove :schedule_4b
      remove :schedule_5b
      remove :schedule_6a
      remove :schedule_7a
      remove :schedule_8a
      remove :schedule_11d
      remove :schedule_17b
      remove :schedule_19
      remove :schedule_22a
      remove :schedule_31d
      remove :schedule_31c
    end
  end

  def down do
    alter table(:tbl_insertion_adj) do
      remove :type
      remove :schedule
    end
  end
end
