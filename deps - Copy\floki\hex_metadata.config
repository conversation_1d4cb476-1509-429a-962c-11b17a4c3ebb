{<<"links">>,
 [{<<"Changelog">>,<<"https://hexdocs.pm/floki/changelog.html">>},
  {<<"GitHub">>,<<"https://github.com/philss/floki">>},
  {<<"Sponsor">>,<<"https://github.com/sponsors/philss">>}]}.
{<<"name">>,<<"floki">>}.
{<<"version">>,<<"0.36.3">>}.
{<<"description">>,
 <<"Floki is a simple HTML parser that enables search for nodes using CSS selectors.">>}.
{<<"elixir">>,<<"~> 1.13">>}.
{<<"app">>,<<"floki">>}.
{<<"licenses">>,[<<"MIT">>]}.
{<<"files">>,
 [<<"lib/floki">>,<<"lib/floki/html_parser">>,
  <<"lib/floki/html_parser/html5ever.ex">>,
  <<"lib/floki/html_parser/fast_html.ex">>,
  <<"lib/floki/html_parser/mochiweb.ex">>,<<"lib/floki/traversal.ex">>,
  <<"lib/floki/text_extractor.ex">>,<<"lib/floki/raw_html.ex">>,
  <<"lib/floki/selector.ex">>,<<"lib/floki/selector">>,
  <<"lib/floki/selector/combinator.ex">>,
  <<"lib/floki/selector/attribute_selector.ex">>,
  <<"lib/floki/selector/tokenizer.ex">>,<<"lib/floki/selector/parser.ex">>,
  <<"lib/floki/selector/functional.ex">>,
  <<"lib/floki/selector/pseudo_class.ex">>,<<"lib/floki/html_tree">>,
  <<"lib/floki/html_tree/id_seeder.ex">>,<<"lib/floki/html_tree/comment.ex">>,
  <<"lib/floki/html_tree/html_node.ex">>,<<"lib/floki/html_tree/text.ex">>,
  <<"lib/floki/finder.ex">>,<<"lib/floki/filter_out.ex">>,
  <<"lib/floki/html_tree.ex">>,<<"lib/floki/html">>,
  <<"lib/floki/html/numeric_charref.ex">>,<<"lib/floki/html/tokenizer.ex">>,
  <<"lib/floki/html_parser.ex">>,<<"lib/floki/deep_text.ex">>,
  <<"lib/floki/entities.ex">>,<<"lib/floki/flat_text.ex">>,
  <<"lib/floki/parse_error.ex">>,<<"lib/floki/entities">>,
  <<"lib/floki/entities/codepoints.ex">>,<<"lib/floki.ex">>,
  <<"src/floki_selector_lexer.xrl">>,<<"src/floki_mochi_html.erl">>,
  <<"mix.exs">>,<<"README.md">>,<<"LICENSE">>,<<"CODE_OF_CONDUCT.md">>,
  <<"CONTRIBUTING.md">>,<<"CHANGELOG.md">>]}.
{<<"requirements">>,[]}.
{<<"build_tools">>,[<<"mix">>]}.
