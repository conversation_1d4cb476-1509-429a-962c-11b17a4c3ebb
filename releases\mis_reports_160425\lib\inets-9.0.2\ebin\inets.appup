%% -*- erlang -*-
%% %CopyrightBegin%
%%
%% Copyright Ericsson AB 1999-2023. All Rights Reserved.
%%
%% Licensed under the Apache License, Version 2.0 (the "License");
%% you may not use this file except in compliance with the License.
%% You may obtain a copy of the License at
%%
%%     http://www.apache.org/licenses/LICENSE-2.0
%%
%% Unless required by applicable law or agreed to in writing, software
%% distributed under the License is distributed on an "AS IS" BASIS,
%% WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
%% See the License for the specific language governing permissions and
%% limitations under the License.
%%
%% %CopyrightEnd%
{"9.0.2",
 [
  {<<"9\\..*">>,[{restart_application, inets}]},
  {<<"8\\..*">>,[{restart_application, inets}]},
  {<<"7\\..*">>,[{restart_application, inets}]},
  {<<"6\\..*">>,[{restart_application, inets}]},
  {<<"5\\..*">>,[{restart_application, inets}]}
 ],
 [
  {<<"9\\..*">>,[{restart_application, inets}]},
  {<<"8\\..*">>,[{restart_application, inets}]},
  {<<"7\\..*">>,[{restart_application, inets}]},
  {<<"6\\..*">>,[{restart_application, inets}]},
  {<<"5\\..*">>,[{restart_application, inets}]}
 ]
}.
