defmodule MisReports.Repo.Migrations.RenameFieldsTblTax do
  use Ecto.Migration

  def up do
    rename table(:tbl_tax), :Unrealised_opening_asset, to: :unrealised_opening_asset
    rename table(:tbl_tax), :Unrealised_mvt_month, to: :unrealised_mvt_month
    rename table(:tbl_tax), :Unrealised_mvt_equity, to: :unrealised_mvt_equity

    create(unique_index(:tbl_tax, [:report_date], name: :unique_report_date))
  end

  def down do
    drop(index(:tbl_tax, [:report_date], name: :unique_report_date))

    rename table(:tbl_tax), :unrealised_opening_asset, to: :Unrealised_opening_asset
    rename table(:tbl_tax), :unrealised_mvt_month, to: :Unrealised_mvt_month
    rename table(:tbl_tax), :unrealised_mvt_equity, to: :Unrealised_mvt_equity

  end
end
