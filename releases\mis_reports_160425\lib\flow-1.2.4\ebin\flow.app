{application,flow,
             [{applications,[kernel,stdlib,elixir,logger,gen_stage]},
              {description,"Computational parallel flows for Elixir"},
              {modules,['Elixir.Enumerable.Flow','Elixir.Flow',
                        'Elixir.Flow.Coordinator','Elixir.Flow.MapReducer',
                        'Elixir.Flow.Materialize','Elixir.Flow.Window',
                        'Elixir.Flow.Window.Count','Elixir.Flow.Window.Fixed',
                        'Elixir.Flow.Window.Global',
                        'Elixir.Flow.Window.Periodic']},
              {registered,[]},
              {vsn,"1.2.4"}]}.
