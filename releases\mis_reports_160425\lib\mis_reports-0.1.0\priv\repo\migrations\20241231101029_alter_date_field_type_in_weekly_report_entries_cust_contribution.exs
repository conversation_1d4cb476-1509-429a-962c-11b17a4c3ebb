defmodule MisReports.Repo.Migrations.AlterDateFieldTypeInWeeklyReportEntriesCustContribution do
  use Ecto.Migration

  def change do
   alter table(:weekly_report_entries_cust_contribution) do
      # Modify the data type of existing fields from :string to :date
      # modify :account_maturity_date, :date
      # modify :account_closed_date, :date
      # modify :account_open_date, :date
      # modify :date, :date
    end
  end
end
