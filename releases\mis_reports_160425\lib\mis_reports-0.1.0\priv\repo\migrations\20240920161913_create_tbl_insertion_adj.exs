defmodule MisReports.Repo.Migrations.CreateTblInsertionAdj do
  use Ecto.Migration

  def change do
    create table(:tbl_insertion_adj) do
      add :schedule_2c, :text
      add :schedule_2g, :text
      add :schedule_3a, :text
      add :schedule_4b, :text
      add :schedule_5b, :text
      add :schedule_6a, :text
      add :schedule_7a, :text
      add :schedule_8a, :text
      add :schedule_11d, :text
      add :schedule_17b, :text
      add :schedule_19, :text
      add :schedule_22a, :text
      add :schedule_31d, :text
      add :schedule_31c, :text
      add :report_date, :date
      add :maker_id, :integer
      add :checker_id, :integer
      add :status, :string

      timestamps()
    end
  end
end
