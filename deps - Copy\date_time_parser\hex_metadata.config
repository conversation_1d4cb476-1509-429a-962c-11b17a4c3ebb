{<<"app">>,<<"date_time_parser">>}.
{<<"build_tools">>,[<<"mix">>]}.
{<<"description">>,
 <<"Parse a string into DateTime, NaiveDateTime, Time, or Date struct.">>}.
{<<"elixir">>,<<">= 1.12.0">>}.
{<<"files">>,
 [<<"lib">>,<<"lib/combinators.ex.exs">>,<<"lib/combinators.ex">>,
  <<"lib/parser">>,<<"lib/parser/date_time_us.ex">>,<<"lib/parser/time.ex">>,
  <<"lib/parser/date_time.ex">>,<<"lib/parser/date.ex">>,
  <<"lib/parser/tokenizer.ex">>,<<"lib/parser/date_us.ex">>,
  <<"lib/parser/epoch.ex">>,<<"lib/parser/serial.ex">>,
  <<"lib/formatters.ex">>,<<"lib/parse_error.ex">>,
  <<"lib/date_time_parser.ex">>,<<"lib/date_time_parser">>,
  <<"lib/date_time_parser/timezone_parser.ex">>,
  <<"lib/date_time_parser/timezone_abbreviations.ex">>,<<"lib/parser.ex">>,
  <<"mix.exs">>,<<"CODE_OF_CONDUCT.md">>,<<"CHANGELOG.md">>,<<"README.md">>,
  <<"LICENSE.md">>,<<"EXAMPLES.livemd">>,<<"priv/tzdata2022g/africa">>,
  <<"priv/tzdata2022g/antarctica">>,<<"priv/tzdata2022g/asia">>,
  <<"priv/tzdata2022g/australasia">>,<<"priv/tzdata2022g/backward">>,
  <<"priv/tzdata2022g/etcetera">>,<<"priv/tzdata2022g/europe">>,
  <<"priv/tzdata2022g/northamerica">>,<<"priv/tzdata2022g/southamerica">>]}.
{<<"licenses">>,[<<"MIT">>]}.
{<<"links">>,
 [{<<"Changelog">>,
   <<"https://github.com/dbernheisel/date_time_parser/blob/1.2.0/CHANGELOG.md">>},
  {<<"GitHub">>,<<"https://github.com/dbernheisel/date_time_parser">>},
  {<<"Readme">>,
   <<"https://github.com/dbernheisel/date_time_parser/blob/1.2.0/README.md">>}]}.
{<<"name">>,<<"date_time_parser">>}.
{<<"requirements">>,
 [[{<<"app">>,<<"kday">>},
   {<<"name">>,<<"kday">>},
   {<<"optional">>,false},
   {<<"repository">>,<<"hexpm">>},
   {<<"requirement">>,<<"~> 1.0">>}]]}.
{<<"version">>,<<"1.2.0">>}.
