defmodule MisReports.Repo.Migrations.CreateWkltaskDetails do
  use Ecto.Migration

  def change do
    create table(:wkltask_details) do
      add :detail_id, :integer
      add :header_id, :integer
      add :step_id, :integer
      add :action_id, :integer
      add :user_id, :string
      add :start_date, :date
      add :end_date, :date
      add :invisible, :boolean, default: false, null: false
      add :comments, :text
      add :upload, :text
      add :privilege_id, :integer
      add :previous_user_id, :string
      add :previous_user_name, :string

      timestamps()
    end
  end
end
